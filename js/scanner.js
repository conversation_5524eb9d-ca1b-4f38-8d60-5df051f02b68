// Scanner module Java<PERSON>

function initScannerModule() {
  const startScannerBtn = document.getElementById("start-scanner");
  const stopScannerBtn = document.getElementById("stop-scanner");
  const qrReaderContainer = document.getElementById("qr-reader-container");
  const scannerResult = document.getElementById("scanner-result");
  const clearHistoryBtn = document.getElementById("clear-history");

  if (!startScannerBtn) {
    return; // Module elements not found
  }

  let html5QrCode; // Scanner instance

  // Função para aguardar o carregamento da biblioteca
  function waitForLibrary(callback, maxAttempts = 20) {
    let attempts = 0;
    const checkInterval = setInterval(() => {
      attempts++;
      if (typeof Html5Qrcode !== 'undefined') {
        clearInterval(checkInterval);
        callback(true);
      } else if (attempts >= maxAttempts) {
        clearInterval(checkInterval);
        callback(false);
      }
    }, 250);
  }

  // --- Event Listeners ---
  startScannerBtn.addEventListener("click", () => {
    // Verificar se a biblioteca Html5Qrcode está disponível
    if (typeof Html5Qrcode === 'undefined') {
      // Tentar aguardar o carregamento da biblioteca
      startScannerBtn.disabled = true;
      startScannerBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> A carregar...';

      waitForLibrary((success) => {
        startScannerBtn.disabled = false;
        startScannerBtn.innerHTML = '<i class="fas fa-camera mr-2"></i> Iniciar Câmera';

        if (!success) {
          alert('Biblioteca de scanner não carregada. Por favor, recarregue a página.');
          return;
        }

        // Tentar iniciar o scanner novamente
        startScanner();
      });
      return;
    }

    startScanner();
  });

  function startScanner() {
    qrReaderContainer.classList.remove("hidden");
    startScannerBtn.classList.add("hidden");
    if (stopScannerBtn) stopScannerBtn.classList.remove("hidden");

    try {
      html5QrCode = new Html5Qrcode("qr-reader");
      const config = {
        fps: 10,
        qrbox: { width: 250, height: 250 },
        aspectRatio: 1.0
      };

      html5QrCode.start(
        { facingMode: "environment" },
        config,
        onScanSuccess,
        (errorMessage) => {
          // Silenciar erros de scan contínuo
        }
      ).then(() => {
        console.log("Scanner iniciado com sucesso");
        showScannerStatus("Scanner ativo - Aponte para um código", "success");
      }).catch((error) => {
        console.error('Erro ao iniciar scanner:', error);
        showScannerStatus("Erro ao iniciar scanner", "error");
        resetScannerUI();
      });
    } catch (error) {
      console.error('Erro ao criar scanner:', error);
      showScannerStatus("Erro ao criar scanner", "error");
      resetScannerUI();
    }
  }

  function showScannerStatus(message, type = "info") {
    // Criar ou atualizar elemento de status
    let statusEl = document.getElementById('scanner-status');
    if (!statusEl) {
      statusEl = document.createElement('div');
      statusEl.id = 'scanner-status';
      statusEl.className = 'mt-2 p-2 rounded text-sm text-center transition-all duration-300';
      qrReaderContainer.parentNode.insertBefore(statusEl, qrReaderContainer.nextSibling);
    }

    // Aplicar estilos baseados no tipo
    statusEl.className = `mt-2 p-2 rounded text-sm text-center transition-all duration-300 ${
      type === 'success' ? 'bg-green-100 text-green-800' :
      type === 'error' ? 'bg-red-100 text-red-800' :
      'bg-blue-100 text-blue-800'
    }`;

    statusEl.textContent = message;
    statusEl.style.opacity = '1';

    // Auto-hide após 3 segundos para mensagens de sucesso
    if (type === 'success') {
      setTimeout(() => {
        if (statusEl) {
          statusEl.style.opacity = '0';
          setTimeout(() => {
            if (statusEl && statusEl.parentNode) {
              statusEl.parentNode.removeChild(statusEl);
            }
          }, 300);
        }
      }, 3000);
    }
  }

  function resetScannerUI() {
    qrReaderContainer.classList.add("hidden");
    startScannerBtn.classList.remove("hidden");
    if (stopScannerBtn) stopScannerBtn.classList.add("hidden");
  }

  if (stopScannerBtn) {
    stopScannerBtn.addEventListener("click", () => {
      if (html5QrCode && html5QrCode.isScanning) {
        html5QrCode.stop().then(() => {
          console.log("Scanner parado com sucesso");
        }).catch(err => {
          console.error("Erro ao parar scanner:", err);
        });
      }
      resetScannerUI();
    });
  }

  scannerResult.addEventListener('click', (e) => {
    const target = e.target.closest('button');
    if (target && target.dataset.action === 'edit') {
      const { type, id } = target.dataset;
      handleEditRedirect(type, id);
    }
  });

  if (clearHistoryBtn) {
    clearHistoryBtn.addEventListener("click", clearScanHistory);
  }

  // --- Initial Load ---
  loadScanHistory();
}

function onScanSuccess(decodedText) {
  // Stop the scanner automatically
  const stopBtn = document.getElementById('stop-scanner');
  if (stopBtn) stopBtn.click();

  // Mostrar feedback de sucesso
  if (typeof showNotification === 'function') {
    showNotification('Código lido com sucesso!', 'success');
  }

  // Check for a globally defined custom success handler
  if (typeof window.onScanSuccess === 'function') {
    window.onScanSuccess(decodedText);
    return;
  }

  // Add to history regardless of format
  addToScanHistory(decodedText);
  addActivityLog("Leitura de Código", `Código: ${decodedText}`);

  const parts = decodedText.split(':');
  if (parts.length !== 2 || !['exams', 'mail'].includes(parts[0])) {
    displayRecord(null, null, 'Código QR inválido ou não reconhecido.');
    return;
  }

  const type = parts[0];
  const id = parts[1];
  
  const record = getRecordById(type, id);
  
  if (record) {
    displayRecord(type, record);
  } else {
    displayRecord(type, null, `Registo do tipo '${type}' com ID '${id}' não encontrado.`);
  }
}

function displayRecord(type, record, errorMessage = '') {
  const scannerResultContainer = document.getElementById("scanner-result");
  const scanResultText = document.getElementById("scan-result-text");
  
  scannerResultContainer.classList.remove("hidden");
  scanResultText.innerHTML = ''; // Clear previous content

  if (!record) {
    scanResultText.innerHTML = `<p class="text-red-500 font-semibold">${errorMessage}</p>`;
    return;
  }

  let detailsHtml = `<h3 class="text-lg font-bold mb-2 text-gray-800">Registo Encontrado</h3>`;
  detailsHtml += '<dl class="space-y-2">';

  // Dynamically create key-value pairs for display
  for (const [key, value] of Object.entries(record)) {
    // Simple mapping for better labels
    const keyMap = {
      patientId: 'Nº Utente', patientName: 'Nome do Utente', examType: 'Tipo de Exame', examDate: 'Data do Exame',
      code: 'Código', sender: 'Remetente', recipient: 'Destinatário', type: 'Tipo', date: 'Data',
      status: 'Estado', notes: 'Observações', description: 'Descrição', registeredBy: 'Registado Por',
      createdAt: 'Data de Registo'
    };
    // Ignore some fields
    if (['id', 'updatedAt'].includes(key)) continue;

    detailsHtml += `
      <div class="grid grid-cols-3 gap-4">
        <dt class="text-sm font-medium text-gray-500 col-span-1">${keyMap[key] || key}</dt>
        <dd class="text-sm text-gray-900 col-span-2">${value || 'N/A'}</dd>
      </div>
    `;
  }
  detailsHtml += '</dl>';

  detailsHtml += `
    <div class="mt-4">
      <button data-action="edit" data-type="${type}" data-id="${record.id}" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
        <i class="fas fa-edit mr-2"></i> Editar Registo
      </button>
    </div>
  `;

  scanResultText.innerHTML = detailsHtml;
}

function handleEditRedirect(type, id) {
  if (type === 'exams') {
    showSection('exams-content');
    // Use a timeout to ensure the module is initialized and ready
    setTimeout(() => handleEditExam(id), 100);
  } else if (type === 'mail') {
    showSection('mail-content');
    setTimeout(() => handleEditMail(id), 100);
  }
}


// --- Scan History Functions (Largely unchanged) ---

let scanHistoryData = JSON.parse(localStorage.getItem("scanHistory")) || [];

function addToScanHistory(code) {
  const scan = { timestamp: new Date().toISOString(), code };
  scanHistoryData.unshift(scan);
  if (scanHistoryData.length > 50) scanHistoryData = scanHistoryData.slice(0, 50);
  localStorage.setItem("scanHistory", JSON.stringify(scanHistoryData));
  loadScanHistory();
}

function loadScanHistory() {
  const scanHistoryBody = document.getElementById("scan-history");
  if (!scanHistoryBody) return;
  scanHistoryBody.innerHTML = "";
  if (scanHistoryData.length === 0) {
    scanHistoryBody.innerHTML = '<tr><td colspan="3" class="px-6 py-4 text-center text-gray-500">Nenhuma leitura registada</td></tr>';
    return;
  }
  scanHistoryData.forEach((scan, index) => {
    const row = document.createElement("tr");
    row.innerHTML = `
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${formatDate(scan.timestamp)}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">${scan.code}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <button onclick="deleteScanResult(${index})" class="text-red-600 hover:text-red-900" title="Apagar do histórico">
          <i class="fas fa-trash"></i>
        </button>
      </td>
    `;
    scanHistoryBody.appendChild(row);
  });
}

function deleteScanResult(index) {
  scanHistoryData.splice(index, 1);
  localStorage.setItem("scanHistory", JSON.stringify(scanHistoryData));
  loadScanHistory();
}

function clearScanHistory() {
  if (confirm("Tem a certeza que deseja limpar todo o histórico de leituras?")) {
    scanHistoryData = [];
    localStorage.setItem("scanHistory", JSON.stringify(scanHistoryData));
    loadScanHistory();
    addActivityLog("Histórico de Leituras", "Histórico limpo");
  }
}