// Users module JavaScript

function initUsersModule() {
  // --- Element Cache ---
  const userForm = document.getElementById('user-form');
  if (!userForm) return;

  const usersTableBody = document.getElementById('users-table-body');
  const searchInput = document.getElementById('search-users');
  const selectAllCheckbox = document.getElementById('select-all-users');
  const deleteSelectedBtn = document.getElementById('delete-selected-users');

  // --- Event Listeners ---
  userForm.addEventListener('submit', handleUserSubmit);
  searchInput.addEventListener('input', () => loadUsersData());
  selectAllCheckbox.addEventListener('change', handleSelectAll);
  deleteSelectedBtn.addEventListener('click', handleDeleteSelected);

  usersTableBody.addEventListener('change', (e) => {
    if (e.target.classList.contains('user-checkbox')) {
      updateDeleteButtonVisibility();
    }
  });

  usersTableBody.addEventListener('click', (e) => {
    const button = e.target.closest('button');
    if (!button) return;
    const { action, id } = button.dataset;
    if (action === 'edit') handleEditUser(id);
    if (action === 'delete') handleDeleteUser(id);
  });

  // --- Initial Load ---
  loadUsersData();
}

function loadUsersData() {
  const usersTableBody = document.getElementById('users-table-body');
  const searchInput = document.getElementById('search-users');
  const searchTerm = searchInput.value.toLowerCase();

  let users = getUsers();

  if (searchTerm) {
    users = users.filter(user =>
      Object.values(user).some(value =>
        String(value).toLowerCase().includes(searchTerm)
      )
    );
  }

  usersTableBody.innerHTML = '';
  if (users.length === 0) {
    usersTableBody.innerHTML = `<tr><td colspan="6" class="px-6 py-4 text-center text-gray-500">Nenhum utilizador encontrado</td></tr>`;
    return;
  }

  users.forEach(user => {
    const row = document.createElement('tr');
    row.dataset.id = user.id;
    row.innerHTML = `
      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <input type="checkbox" class="user-checkbox rounded border-gray-300" data-id="${user.id}" ${user.id == 1 ? 'disabled' : ''}>
      </td>
      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${user.name}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.username}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${user.email}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${user.role}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <button data-action="edit" data-id="${user.id}" class="text-blue-600 hover:text-blue-900 mr-3" title="Editar"><i class="fas fa-edit"></i></button>
        <button data-action="delete" data-id="${user.id}" class="text-red-600 hover:text-red-900" title="Apagar" ${user.id == 1 ? 'disabled' : ''}><i class="fas fa-trash"></i></button>
      </td>
    `;
    usersTableBody.appendChild(row);
  });
  updateDeleteButtonVisibility();
}

function handleUserSubmit(e) {
  e.preventDefault();
  const userDataPayload = {
    name: document.getElementById('user-name').value,
    username: document.getElementById('user-username').value,
    email: document.getElementById('user-email').value,
    role: document.getElementById('user-role').value,
  };

  if (!userDataPayload.name || !userDataPayload.username || !userDataPayload.email) {
    alert('Por favor, preencha todos os campos obrigatórios.');
    return;
  }

  const editingId = document.getElementById('user-id-editing').value;
  if (editingId) {
    updateUser(editingId, userDataPayload);
  } else {
    addNewUser(userDataPayload);
  }

  document.getElementById('user-form').reset();
  document.getElementById('user-id-editing').value = '';
  loadUsersData();
  updateCounters();
}

function handleEditUser(id) {
  const user = getUsers().find(u => u.id == id);
  if (!user) return;
  document.getElementById('user-id-editing').value = user.id;
  document.getElementById('user-name').value = user.name;
  document.getElementById('user-username').value = user.username;
  document.getElementById('user-email').value = user.email;
  document.getElementById('user-role').value = user.role;
  document.getElementById('user-form').scrollIntoView({ behavior: 'smooth' });
}

function handleDeleteUser(id) {
  if (id == 1) {
    alert('Não é possível apagar o utilizador Administrador principal.');
    return;
  }
  if (confirm('Tem a certeza que deseja apagar este utilizador?')) {
    let users = getUsers();
    users = users.filter(u => u.id != id);
    saveUsers(users);
    loadUsersData();
    updateCounters();
  }
}

function handleSelectAll(e) {
  const checkboxes = document.querySelectorAll('.user-checkbox:not(:disabled)');
  const isChecked = e.target.checked;
  checkboxes.forEach(checkbox => {
    checkbox.checked = isChecked;
  });
  updateDeleteButtonVisibility();
}

function updateDeleteButtonVisibility() {
  const deleteSelectedBtn = document.getElementById('delete-selected-users');
  const selectedCount = document.querySelectorAll('.user-checkbox:checked').length;
  deleteSelectedBtn.classList.toggle('hidden', selectedCount === 0);
}

function handleDeleteSelected() {
  const selectedIds = [...document.querySelectorAll('.user-checkbox:checked')].map(cb => cb.dataset.id);
  if (selectedIds.length === 0) return;
  if (confirm(`Tem a certeza que deseja apagar os ${selectedIds.length} utilizadores selecionados?`)) {
    let users = getUsers();
    users = users.filter(u => !selectedIds.includes(String(u.id)));
    saveUsers(users);
    loadUsersData();
    updateCounters();
  }
}

// --- Data Functions ---
function getUsers() { return JSON.parse(localStorage.getItem('hospitalUsers')) || []; }
function saveUsers(users) {
  localStorage.setItem('hospitalUsers', JSON.stringify(users));
  if (typeof userData !== 'undefined') userData = users;
}
function addNewUser(userPayload) {
  const users = getUsers();
  users.push({ ...userPayload, id: Date.now(), active: true });
  saveUsers(users);
}
function updateUser(id, userPayload) {
  const users = getUsers();
  const userIndex = users.findIndex(u => u.id == id);
  if (userIndex > -1) {
    users[userIndex] = { ...users[userIndex], ...userPayload };
    saveUsers(users);
  }
}