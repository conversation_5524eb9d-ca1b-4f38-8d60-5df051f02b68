// Dashboard module JavaScript

function initDashboardModule() {
  // The dashboard is the default view, so we just need to ensure
  // its dynamic content is up-to-date when the application loads
  // or when the user navigates back to it.
  
  if (!document.getElementById('dashboard-content').classList.contains('hidden')) {
    refreshDashboard();
  }
}

function refreshDashboard() {
  // This function will be called to update the dashboard stats and logs.
  if (typeof updateCounters === 'function') {
    updateCounters();
  }
  if (typeof loadActivityLogs === 'function') {
    loadActivityLogs();
  }
}

// Ensure the dashboard is refreshed when its link is clicked
document.addEventListener('DOMContentLoaded', () => {
  const dashboardLink = document.getElementById('dashboard-link');
  if (dashboardLink) {
    dashboardLink.addEventListener('click', () => {
      // A small delay to ensure the section is visible before refreshing
      setTimeout(refreshDashboard, 50);
    });
  }
  
  // Initial refresh on load
  refreshDashboard();
});
