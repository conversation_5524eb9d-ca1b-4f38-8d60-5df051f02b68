// Dashboard module JavaScript

// Variáveis globais para os gráficos
let examsStatusChart = null;
let activityChart = null;
let currentActivityPage = 0;
let filteredActivities = [];
let allActivities = [];

function initDashboardModule() {
  // Verificar se já foi inicializado para evitar duplicação
  if (window.dashboardInitialized) {
    refreshDashboard();
    return;
  }

  // Configurar event listeners
  setupDashboardEventListeners();

  if (!document.getElementById('dashboard-content').classList.contains('hidden')) {
    refreshDashboard();
  }

  window.dashboardInitialized = true;
}

function setupDashboardEventListeners() {
  const activitySearch = document.getElementById('activity-search');
  const clearSearch = document.getElementById('clear-activity-search');
  const loadMoreBtn = document.getElementById('load-more-activities');

  if (activitySearch) {
    activitySearch.addEventListener('input', handleActivitySearch);
  }

  if (clearSearch) {
    clearSearch.addEventListener('click', () => {
      activitySearch.value = '';
      handleActivitySearch();
    });
  }

  if (loadMoreBtn) {
    loadMoreBtn.addEventListener('click', loadMoreActivities);
  }

  // Event listeners para os atalhos rápidos
  const quickActionButtons = document.querySelectorAll('#dashboard-content [data-section]');
  quickActionButtons.forEach(button => {
    button.addEventListener('click', (e) => {
      e.preventDefault();
      const sectionId = button.dataset.section;
      if (typeof showSection === 'function') {
        showSection(sectionId);
      }
    });
  });
}

function refreshDashboard() {
  // Atualizar contadores e estatísticas
  if (typeof updateCounters === 'function') {
    updateCounters();
  }

  updateAdvancedStats();
  loadAllActivityLogs();
  createCharts();
}

function updateAdvancedStats() {
  const exams = JSON.parse(localStorage.getItem('hospitalExams')) || [];
  const mail = JSON.parse(localStorage.getItem('hospitalMail')) || [];
  const users = JSON.parse(localStorage.getItem('hospitalUsers')) || [];
  const activities = JSON.parse(localStorage.getItem('activityLogs')) || [];

  // Calcular tendências (simulado - em produção seria baseado em dados históricos)
  const examsTrend = document.getElementById('exams-trend');
  const mailTrend = document.getElementById('mail-trend');
  const usersActive = document.getElementById('users-active');
  const todayActivities = document.getElementById('today-activities');
  const lastActivity = document.getElementById('last-activity');

  if (examsTrend) {
    const trend = Math.floor(Math.random() * 20) - 5; // Simulação
    examsTrend.textContent = `${trend >= 0 ? '+' : ''}${trend}% este mês`;
    examsTrend.className = `text-sm ${trend >= 0 ? 'text-green-600' : 'text-red-600'}`;
  }

  if (mailTrend) {
    const trend = Math.floor(Math.random() * 15) - 3; // Simulação
    mailTrend.textContent = `${trend >= 0 ? '+' : ''}${trend}% este mês`;
    mailTrend.className = `text-sm ${trend >= 0 ? 'text-green-600' : 'text-red-600'}`;
  }

  if (usersActive) {
    const activeUsers = users.filter(u => u.active !== false).length;
    usersActive.textContent = `${activeUsers} ${activeUsers === 1 ? 'ativo' : 'ativos'}`;
  }

  // Atividades de hoje
  const today = new Date().toDateString();
  const todayActivitiesCount = activities.filter(activity =>
    new Date(activity.timestamp).toDateString() === today
  ).length;

  if (todayActivities) {
    todayActivities.textContent = todayActivitiesCount;
  }

  if (lastActivity && activities.length > 0) {
    const last = activities[0];
    const timeDiff = Date.now() - new Date(last.timestamp).getTime();
    const minutes = Math.floor(timeDiff / 60000);
    const hours = Math.floor(minutes / 60);

    let timeText;
    if (minutes < 1) timeText = 'Agora mesmo';
    else if (minutes < 60) timeText = `Há ${minutes} min`;
    else if (hours < 24) timeText = `Há ${hours}h`;
    else timeText = 'Há mais de 1 dia';

    lastActivity.textContent = timeText;
  }
}

function loadAllActivityLogs() {
  allActivities = JSON.parse(localStorage.getItem('activityLogs')) || [];
  filteredActivities = [...allActivities];
  currentActivityPage = 0;
  renderActivities();
}

function handleActivitySearch() {
  const searchTerm = document.getElementById('activity-search').value.toLowerCase();

  if (!searchTerm.trim()) {
    filteredActivities = [...allActivities];
  } else {
    filteredActivities = allActivities.filter(activity =>
      activity.action.toLowerCase().includes(searchTerm) ||
      activity.details.toLowerCase().includes(searchTerm) ||
      activity.user.toLowerCase().includes(searchTerm) ||
      formatDate(activity.timestamp).toLowerCase().includes(searchTerm)
    );
  }

  currentActivityPage = 0;
  renderActivities();
}

function renderActivities() {
  const activityContainer = document.getElementById('recent-activity');
  const loadMoreContainer = document.getElementById('activity-load-more');

  if (!activityContainer) return;

  const itemsPerPage = 10;
  const startIndex = currentActivityPage * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const activitiesToShow = filteredActivities.slice(0, endIndex);

  if (currentActivityPage === 0) {
    activityContainer.innerHTML = '';
  }

  if (filteredActivities.length === 0) {
    activityContainer.innerHTML = `
      <tr>
        <td colspan="4" class="px-6 py-8 text-center text-gray-500">
          <div class="flex flex-col items-center">
            <i class="fas fa-search text-3xl mb-3 text-gray-400"></i>
            <p class="text-lg font-medium">Nenhuma atividade encontrada</p>
            <p class="text-sm mt-1">Tente ajustar os termos de pesquisa</p>
          </div>
        </td>
      </tr>
    `;
    if (loadMoreContainer) loadMoreContainer.classList.add('hidden');
    return;
  }

  // Adicionar apenas as novas atividades (corrigido)
  const currentlyShown = activityContainer.children.length;
  const newActivities = filteredActivities.slice(currentlyShown, endIndex);

  newActivities.forEach(activity => {
    const row = document.createElement('tr');
    row.className = 'hover:bg-gray-50 transition-colors duration-200';
    row.innerHTML = `
      <td class="px-3 sm:px-6 py-4 text-sm text-gray-500 whitespace-nowrap">${formatDate(activity.timestamp)}</td>
      <td class="px-3 sm:px-6 py-4 text-sm font-medium text-gray-900">${activity.user}</td>
      <td class="px-3 sm:px-6 py-4 text-sm">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getActivityBadgeClass(activity.action)}">
          ${activity.action}
        </span>
      </td>
      <td class="px-3 sm:px-6 py-4 text-sm text-gray-500 hidden sm:table-cell">${activity.details}</td>
    `;
    activityContainer.appendChild(row);
  });

  // Mostrar/ocultar botão "Carregar mais"
  if (loadMoreContainer) {
    const totalShown = activityContainer.children.length;
    if (totalShown < filteredActivities.length) {
      loadMoreContainer.classList.remove('hidden');
      // Atualizar texto do botão
      const remainingCount = filteredActivities.length - totalShown;
      const loadMoreBtn = document.getElementById('load-more-activities');
      if (loadMoreBtn) {
        loadMoreBtn.innerHTML = `Carregar mais ${Math.min(remainingCount, itemsPerPage)} atividades (${remainingCount} restantes)`;
      }
    } else {
      loadMoreContainer.classList.add('hidden');
    }
  }
}

function loadMoreActivities() {
  currentActivityPage++;
  renderActivities();
}

function getActivityBadgeClass(action) {
  if (action.includes('Novo')) return 'bg-green-100 text-green-800';
  if (action.includes('Atualizado')) return 'bg-blue-100 text-blue-800';
  if (action.includes('Removido')) return 'bg-red-100 text-red-800';
  return 'bg-gray-100 text-gray-800';
}

function createCharts() {
  createExamsStatusChart();
  createActivityChart();
}

function createExamsStatusChart() {
  const ctx = document.getElementById('exams-status-chart');
  if (!ctx) return;

  // Mostrar indicador de carregamento
  const chartContainer = ctx.parentElement;
  chartContainer.innerHTML = '<div class="chart-loading">A carregar gráfico...</div>';

  setTimeout(() => {
    // Restaurar canvas
    chartContainer.innerHTML = '<canvas id="exams-status-chart"></canvas>';
    const newCtx = document.getElementById('exams-status-chart');

    const exams = JSON.parse(localStorage.getItem('hospitalExams')) || [];

  // Contar exames por estado
  const statusCounts = {
    'Agendado': 0,
    'Realizado': 0,
    'Cancelado': 0
  };

  exams.forEach(exam => {
    if (statusCounts.hasOwnProperty(exam.examStatus)) {
      statusCounts[exam.examStatus]++;
    }
  });

  // Destruir gráfico existente se houver
  if (examsStatusChart) {
    examsStatusChart.destroy();
  }

  examsStatusChart = new Chart(newCtx, {
    type: 'doughnut',
    data: {
      labels: Object.keys(statusCounts),
      datasets: [{
        data: Object.values(statusCounts),
        backgroundColor: [
          '#FCD34D', // Amarelo para Agendado
          '#10B981', // Verde para Realizado
          '#EF4444'  // Vermelho para Cancelado
        ],
        borderWidth: 2,
        borderColor: '#ffffff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            padding: 20,
            usePointStyle: true
          }
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const total = context.dataset.data.reduce((a, b) => a + b, 0);
              const percentage = total > 0 ? ((context.parsed * 100) / total).toFixed(1) : 0;
              return `${context.label}: ${context.parsed} (${percentage}%)`;
            }
          }
        }
      }
    }
  });
  }, 500); // Delay para mostrar o loading
}

function createActivityChart() {
  const ctx = document.getElementById('activity-chart');
  if (!ctx) return;

  // Mostrar indicador de carregamento
  const chartContainer = ctx.parentElement;
  chartContainer.innerHTML = '<div class="chart-loading">A carregar gráfico...</div>';

  setTimeout(() => {
    // Restaurar canvas
    chartContainer.innerHTML = '<canvas id="activity-chart"></canvas>';
    const newCtx = document.getElementById('activity-chart');

    const activities = JSON.parse(localStorage.getItem('activityLogs')) || [];

  // Obter dados dos últimos 7 dias
  const last7Days = [];
  const activityCounts = [];

  for (let i = 6; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    const dateString = date.toDateString();

    last7Days.push(date.toLocaleDateString('pt-PT', {
      weekday: 'short',
      day: 'numeric'
    }));

    const dayActivities = activities.filter(activity =>
      new Date(activity.timestamp).toDateString() === dateString
    ).length;

    activityCounts.push(dayActivities);
  }

  // Destruir gráfico existente se houver
  if (activityChart) {
    activityChart.destroy();
  }

  activityChart = new Chart(newCtx, {
    type: 'line',
    data: {
      labels: last7Days,
      datasets: [{
        label: 'Atividades',
        data: activityCounts,
        borderColor: '#3B82F6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#3B82F6',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 4
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            stepSize: 1
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          }
        },
        x: {
          grid: {
            display: false
          }
        }
      }
    }
  });
  }, 500); // Delay para mostrar o loading
}

// Função para limpar gráficos quando necessário
function destroyCharts() {
  if (examsStatusChart) {
    examsStatusChart.destroy();
    examsStatusChart = null;
  }
  if (activityChart) {
    activityChart.destroy();
    activityChart = null;
  }
}

// Event listeners para o dashboard
document.addEventListener('DOMContentLoaded', () => {
  const dashboardLink = document.getElementById('dashboard-link');
  if (dashboardLink) {
    dashboardLink.addEventListener('click', () => {
      // Pequeno delay para garantir que a secção está visível
      setTimeout(() => {
        refreshDashboard();
      }, 100);
    });
  }

  // Refresh inicial
  if (!document.getElementById('dashboard-content').classList.contains('hidden')) {
    setTimeout(refreshDashboard, 100);
  }
});

// Função para atualizar o dashboard quando há mudanças nos dados
function updateDashboardData() {
  if (!document.getElementById('dashboard-content').classList.contains('hidden')) {
    refreshDashboard();
  }
}

// Função auxiliar para formatar datas (caso não esteja disponível no main.js)
function formatDate(dateString) {
  const date = new Date(dateString);
  return `${date.toLocaleDateString('pt-PT')} ${date.toLocaleTimeString('pt-PT', {
    hour: '2-digit',
    minute: '2-digit'
  })}`;
}
