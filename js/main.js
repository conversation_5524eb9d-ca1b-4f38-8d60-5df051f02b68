// Main JavaScript file for the Hospital Records System

// --- START: Data Storage and Management (unchanged) ---
let userData = JSON.parse(localStorage.getItem("hospitalUsers")) || [
  { id: 1, name: "Admin", email: "<EMAIL>", username: "admin", role: "Administrador", active: true },
];
let examsData = JSON.parse(localStorage.getItem("hospitalExams")) || [];
let mailData = JSON.parse(localStorage.getItem("hospitalMail")) || [];
let activityLogs = JSON.parse(localStorage.getItem("activityLogs")) || [];

function generateUniqueId() { return Date.now().toString(); }
function getAllRecords(type) {
  const keyMap = { exams: 'hospitalExams', mail: 'hospitalMail' };
  return JSON.parse(localStorage.getItem(keyMap[type])) || [];
}
function saveRecord(type, data) {
  const records = getAllRecords(type);
  const newRecord = { ...data, id: generateUniqueId(), createdAt: new Date().toISOString(), updatedAt: new Date().toISOString(), registeredBy: document.getElementById("current-user")?.textContent || 'Admin' };
  records.push(newRecord);
  const key = type === 'exams' ? 'hospitalExams' : 'hospitalMail';
  localStorage.setItem(key, JSON.stringify(records));
  if (type === 'exams') examsData = records; else mailData = records;
  updateCounters();
  addActivityLog(`Novo Registo: ${type}`, `ID: ${newRecord.id}`);
  return newRecord;
}
function getRecordById(type, id) { return getAllRecords(type).find(r => r.id === id); }
function updateRecord(type, id, data) {
  const records = getAllRecords(type);
  const index = records.findIndex(r => r.id === id);
  if (index === -1) return null;
  records[index] = { ...records[index], ...data, updatedAt: new Date().toISOString() };
  const key = type === 'exams' ? 'hospitalExams' : 'hospitalMail';
  localStorage.setItem(key, JSON.stringify(records));
  if (type === 'exams') examsData = records; else mailData = records;
  addActivityLog(`Registo Atualizado: ${type}`, `ID: ${id}`);
  return records[index];
}
function deleteRecord(type, id) {
  let records = getAllRecords(type);
  const initialLength = records.length;
  records = records.filter(r => r.id !== id);
  if (records.length === initialLength) return false;
  const key = type === 'exams' ? 'hospitalExams' : 'hospitalMail';
  localStorage.setItem(key, JSON.stringify(records));
  if (type === 'exams') examsData = records; else mailData = records;
  updateCounters();
  addActivityLog(`Registo Removido: ${type}`, `ID: ${id}`);
  return true;
}
// --- END: Data Storage and Management ---


// --- START: Application Initialization and Navigation ---
document.addEventListener("DOMContentLoaded", () => {
  setupEventListeners();
  showSection('dashboard-content'); // Show dashboard by default
});

function setupEventListeners() {
  document.getElementById("toggle-sidebar").addEventListener("click", () => {
    document.querySelector(".sidebar").classList.toggle("collapsed");
  });

  document.querySelectorAll("[data-section]").forEach((button) => {
    button.addEventListener("click", (e) => {
      e.preventDefault();
      const sectionId = button.dataset.section;
      showSection(sectionId);
    });
  });
}

function showSection(sectionId) {
  const pageTitle = document.getElementById("page-title");
  
  // Hide all sections
  document.querySelectorAll(".content-section").forEach((section) => {
    section.classList.add("hidden");
  });

  // Show the selected section
  const activeSection = document.getElementById(sectionId);
  if (activeSection) {
    activeSection.classList.remove("hidden");
  }

  // Update page title
  const titles = { "dashboard-content": "Dashboard", "exams-content": "Registo de Exames", "mail-content": "Correio Interno", "users-content": "Gestão de Utilizadores", "reports-content": "Relatórios", "scanner-content": "Leitor de Códigos" };
  pageTitle.textContent = titles[sectionId] || 'Page';

  // Update active link in sidebar
  document.querySelectorAll("[data-section]").forEach(btn => {
    btn.classList.toggle('bg-blue-700', btn.dataset.section === sectionId);
  });

  // Initialize or refresh the module for the shown section
  const moduleName = sectionId.replace('-content', '');
  const initFunctionName = `init${moduleName.charAt(0).toUpperCase() + moduleName.slice(1)}Module`;
  if (typeof window[initFunctionName] === 'function') {
    // Use a timeout to ensure the DOM is ready for the script
    setTimeout(window[initFunctionName], 0);
  }
}
// --- END: Application Initialization and Navigation ---


// --- START: Global Select All Handler ---
function setupSelectAllHandler(checkboxId, itemClass, updateFunction) {
  const selectAllCheckbox = document.getElementById(checkboxId);
  if (!selectAllCheckbox) return;

  // Remover event listener existente se houver
  selectAllCheckbox.removeEventListener('change', selectAllCheckbox._handleSelectAll);

  // Criar nova função handler
  const handleSelectAll = function(e) {
    const isChecked = e.target.checked;
    const checkboxes = document.querySelectorAll(`.${itemClass}`);

    checkboxes.forEach(checkbox => {
      if (!checkbox.disabled) {
        checkbox.checked = isChecked;

        // Aplicar feedback visual
        const row = checkbox.closest('tr');
        if (row) {
          if (isChecked) {
            row.classList.add('bg-blue-50');
          } else {
            row.classList.remove('bg-blue-50');
          }
        }
      }
    });

    if (updateFunction) updateFunction();
  };

  // Guardar referência para poder remover depois
  selectAllCheckbox._handleSelectAll = handleSelectAll;
  selectAllCheckbox.addEventListener('change', handleSelectAll);
}
// --- END: Global Select All Handler ---


// --- START: UI and Utility Functions (unchanged) ---
function updateCounters() {
  const totalExams = document.getElementById("total-exams");
  const totalMail = document.getElementById("total-mail");
  const totalUsers = document.getElementById("total-users");
  if(totalExams) totalExams.textContent = (JSON.parse(localStorage.getItem("hospitalExams")) || []).length;
  if(totalMail) totalMail.textContent = (JSON.parse(localStorage.getItem("hospitalMail")) || []).length;
  if(totalUsers) totalUsers.textContent = (JSON.parse(localStorage.getItem("hospitalUsers")) || []).length;
}

function loadActivityLogs() {
  const activityContainer = document.getElementById("recent-activity");
  if (!activityContainer) return;
  activityContainer.innerHTML = "";
  const logs = (JSON.parse(localStorage.getItem("activityLogs")) || []).slice(0, 10);
  if (logs.length === 0) {
    activityContainer.innerHTML = `<tr><td colspan="4" class="px-6 py-4 text-center text-gray-500">Nenhuma atividade registada</td></tr>`;
    return;
  }
  logs.forEach(log => {
    const row = document.createElement("tr");
    row.innerHTML = `<td class="px-6 py-4 text-sm text-gray-500">${formatDate(log.timestamp)}</td><td class="px-6 py-4 text-sm">${log.user}</td><td class="px-6 py-4 text-sm">${log.action}</td><td class="px-6 py-4 text-sm text-gray-500">${log.details}</td>`;
    activityContainer.appendChild(row);
  });
}

function addActivityLog(action, details) {
  const logs = JSON.parse(localStorage.getItem("activityLogs")) || [];
  const log = { timestamp: new Date().toISOString(), user: document.getElementById("current-user")?.textContent || 'Admin', action, details };
  logs.unshift(log);
  localStorage.setItem("activityLogs", JSON.stringify(logs.slice(0, 100)));
  if (document.getElementById('recent-activity')) loadActivityLogs();
}

function formatDate(dateString) {
  const date = new Date(dateString);
  return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
}

function showQrCodeModal(type, id) {
  const qrCodeModal = document.getElementById('qr-code-modal');
  const qrCodeContainer = document.getElementById('qr-code-container');
  const qrCodeText = document.getElementById('qr-code-text');
  if (!qrCodeModal || !qrCodeContainer || !qrCodeText) return;
  
  qrCodeContainer.innerHTML = '';
  const qrContent = `${type}:${id}`;
  qrCodeText.textContent = qrContent;
  
  QRCode.toCanvas(document.createElement('canvas'), qrContent, { width: 256 }, (error, canvas) => {
    if (error) {
      qrCodeContainer.textContent = 'Erro ao gerar QR Code.';
      return;
    }
    qrCodeContainer.appendChild(canvas);
    qrCodeModal.classList.remove('hidden');
  });
}

function convertArrayOfObjectsToCSV(data) {
  if (!data || data.length === 0) return '';
  const columnHeaders = Object.keys(data[0]);
  const csvRows = [columnHeaders.join(',')];
  for (const row of data) {
    const values = columnHeaders.map(header => `"${String(row[header]).replace(/"/g, '""')}"`);
    csvRows.push(values.join(','));
  }
  return csvRows.join('\n');
}

function downloadCSV(csvString, filename) {
  const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

function printTable(tableId) {
  const tableElement = document.getElementById(tableId);
  if (!tableElement) return;
  const printWindow = window.open("", "_blank");
  printWindow.document.write(`<html><head><title>Print</title><style>body{font-family:Arial,sans-serif}table{border-collapse:collapse;width:100%}th,td{border:1px solid #ddd;padding:8px;text-align:left}th{background-color:#f2f2f2}</style></head><body><h1>Hospital Records System</h1>${tableElement.outerHTML}</body></html>`);
  printWindow.document.close();
  printWindow.focus();
  printWindow.print();
  printWindow.close();
}
// --- END: UI and Utility Functions ---
