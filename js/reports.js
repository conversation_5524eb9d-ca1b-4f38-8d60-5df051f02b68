// Reports module JavaScript

let examsByType<PERSON>hart = null;
let activityByDay<PERSON>hart = null;

function initReportsModule() {
  if (!document.getElementById('reports-content').classList.contains('hidden')) {
    generateReports();
  }
}

function generateReports() {
  const exams = getAllRecords('exams');
  const mail = getAllRecords('mail');

  generateExamsByTypeChart(exams);
  generateActivityByDayChart(exams, mail);
}

function generateExamsByTypeChart(exams) {
  const ctx = document.getElementById('exams-by-type-chart');
  if (!ctx) return;

  const examCounts = exams.reduce((acc, exam) => {
    acc[exam.examType] = (acc[exam.examType] || 0) + 1;
    return acc;
  }, {});

  const chartData = {
    labels: Object.keys(examCounts),
    datasets: [{
      label: 'Nº de Exames',
      data: Object.values(examCounts),
      backgroundColor: [
        'rgba(54, 162, 235, 0.7)',
        'rgba(255, 99, 132, 0.7)',
        'rgba(255, 206, 86, 0.7)',
        'rgba(75, 192, 192, 0.7)',
        'rgba(153, 102, 255, 0.7)',
        'rgba(255, 159, 64, 0.7)',
        'rgba(99, 255, 132, 0.7)'
      ],
      borderColor: '#fff',
      borderWidth: 1
    }]
  };

  if (examsByTypeChart) {
    examsByTypeChart.destroy();
  }

  examsByTypeChart = new Chart(ctx, {
    type: 'pie',
    data: chartData,
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'top',
        },
        title: {
          display: false,
          text: 'Distribuição de Exames por Tipo'
        }
      }
    }
  });
}

function generateActivityByDayChart(exams, mail) {
  const ctx = document.getElementById('activity-by-day-chart');
  if (!ctx) return;

  const last7Days = Array(7).fill(0).map((_, i) => {
    const d = new Date();
    d.setDate(d.getDate() - i);
    return d.toISOString().split('T')[0];
  }).reverse();

  const activityCounts = last7Days.reduce((acc, day) => {
    acc[day] = 0;
    return acc;
  }, {});

  [...exams, ...mail].forEach(record => {
    const recordDate = record.createdAt.split('T')[0];
    if (activityCounts.hasOwnProperty(recordDate)) {
      activityCounts[recordDate]++;
    }
  });

  const chartData = {
    labels: last7Days.map(d => new Date(d).toLocaleDateString('pt-PT', {day: '2-digit', month: '2-digit'})),
    datasets: [{
      label: 'Nº de Registos',
      data: Object.values(activityCounts),
      backgroundColor: 'rgba(75, 192, 192, 0.7)',
      borderColor: 'rgba(75, 192, 192, 1)',
      borderWidth: 1,
      barThickness: 20,
    }]
  };

  if (activityByDayChart) {
    activityByDayChart.destroy();
  }

  activityByDayChart = new Chart(ctx, {
    type: 'bar',
    data: chartData,
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            stepSize: 1
          }
        }
      },
      plugins: {
        legend: {
          display: false,
        }
      }
    }
  });
}
