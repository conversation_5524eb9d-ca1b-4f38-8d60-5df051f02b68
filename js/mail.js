// Mail module JavaScript

function initMailModule() {
  // --- Element Cache ---
  const mailForm = document.getElementById('mail-form');
  if (!mailForm) return;

  const mailTableBody = document.getElementById('mail-table-body');
  const searchInput = document.getElementById('search-mail');
  const selectAllCheckbox = document.getElementById('select-all-mail');
  const deleteSelectedBtn = document.getElementById('delete-selected-mail');

  const exportMailBtn = document.getElementById('export-mail');
  const printMailBtn = document.getElementById('print-mail');
  const scanMailBtn = document.getElementById('scan-mail');

  // --- Event Listeners ---
  mailForm.addEventListener('submit', handleMailSubmit);
  searchInput.addEventListener('input', () => loadMailData());
  selectAllCheckbox.addEventListener('change', handleSelectAll);
  deleteSelectedBtn.addEventListener('click', handleDeleteSelected);

  mailTableBody.addEventListener('change', (e) => {
    if (e.target.classList.contains('mail-checkbox')) {
      updateDeleteButtonVisibility();
    }
  });

  mailTableBody.addEventListener('click', (e) => {
    const button = e.target.closest('button');
    if (!button) return;
    const { action, id } = button.dataset;
    if (action === 'edit') handleEditMail(id);
    if (action === 'delete') handleDeleteMail(id);
  });

  if (scanMailBtn) scanMailBtn.addEventListener('click', handleScan);
  if (exportMailBtn) exportMailBtn.addEventListener('click', handleExportMail);
  if (printMailBtn) printMailBtn.addEventListener('click', () => printTable('mail-table'));

  // --- Initial Load ---
  loadMailData();
  if (!document.getElementById('mail-id-editing')) {
    const hiddenInput = document.createElement('input');
    hiddenInput.type = 'hidden';
    hiddenInput.id = 'mail-id-editing';
    mailForm.appendChild(hiddenInput);
  }
}

function loadMailData() {
  const mailTableBody = document.getElementById('mail-table-body');
  const searchInput = document.getElementById('search-mail');
  const searchTerm = searchInput.value.toLowerCase();

  let mails = getAllRecords('mail');

  if (searchTerm) {
    mails = mails.filter(mail =>
      Object.values(mail).some(value =>
        String(value).toLowerCase().includes(searchTerm)
      )
    );
  }

  mails.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

  mailTableBody.innerHTML = '';
  if (mails.length === 0) {
    mailTableBody.innerHTML = `<tr><td colspan="9" class="px-6 py-4 text-center text-gray-500">Nenhum correio encontrado</td></tr>`;
    return;
  }

  mails.forEach(mail => {
    const row = document.createElement('tr');
    row.dataset.id = mail.id;
    
    // Criar a célula do checkbox
    const checkboxCell = document.createElement('td');
    checkboxCell.className = "px-6 py-4 whitespace-nowrap text-sm font-medium";
    const checkbox = document.createElement('input');
    checkbox.type = "checkbox";
    checkbox.className = "mail-checkbox rounded border-gray-300 text-blue-600";
    checkbox.dataset.id = mail.id;
    checkboxCell.appendChild(checkbox);
    row.appendChild(checkboxCell);
    
    // Adicionar o resto das células
    row.innerHTML += `
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${mail.code || 'N/A'}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${mail.sender}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${mail.recipient}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${mail.type}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${mail.date}</td>
      <td class="px-6 py-4 whitespace-nowrap">
        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${mail.status === 'Registado' ? 'bg-blue-100 text-blue-800' : mail.status === 'Entregue' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
          ${mail.status}
        </span>
      </td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${mail.registeredBy}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <button data-action="edit" data-id="${mail.id}" class="text-blue-600 hover:text-blue-900 mr-3" title="Editar"><i class="fas fa-edit"></i></button>
        <button data-action="delete" data-id="${mail.id}" class="text-red-600 hover:text-red-900" title="Apagar"><i class="fas fa-trash"></i></button>
      </td>
    `;
    mailTableBody.appendChild(row);
  });
  updateDeleteButtonVisibility();
}

function handleMailSubmit(e) {
  e.preventDefault();
  const mailData = {
    code: document.getElementById('mail-code').value,
    sender: document.getElementById('mail-sender').value,
    recipient: document.getElementById('mail-recipient').value,
    type: document.getElementById('mail-type').value,
    date: document.getElementById('mail-date').value,
    status: document.getElementById('mail-status').value,
    description: document.getElementById('mail-description').value,
  };

  if (!mailData.code || !mailData.sender || !mailData.recipient || !mailData.type || !mailData.date) {
    alert('Por favor, preencha todos os campos obrigatórios (Código, Remetente, Destinatário, Tipo, Data).');
    return;
  }

  const editingId = document.getElementById('mail-id-editing').value;
  const savedRecord = editingId ? updateRecord('mail', editingId, mailData) : saveRecord('mail', mailData);

  if (savedRecord && !editingId) showQrCodeModal('mail', savedRecord.id);

  e.target.reset();
  document.getElementById('mail-id-editing').value = '';
  loadMailData();
}

function handleEditMail(id) {
  const mail = getRecordById('mail', id);
  if (!mail) return;
  document.getElementById('mail-code').value = mail.code || '';
  document.getElementById('mail-sender').value = mail.sender;
  document.getElementById('mail-recipient').value = mail.recipient;
  document.getElementById('mail-type').value = mail.type;
  document.getElementById('mail-date').value = mail.date;
  document.getElementById('mail-status').value = mail.status;
  document.getElementById('mail-description').value = mail.description;
  document.getElementById('mail-id-editing').value = id;
  document.getElementById('mail-form').scrollIntoView({ behavior: 'smooth' });
}

function handleDeleteMail(id) {
  if (confirm('Tem a certeza que deseja apagar este registo de correio?')) {
    if (deleteRecord('mail', id)) loadMailData();
  }
}

function handleSelectAll(e) {
  const isChecked = e.target.checked;
  const checkboxes = document.querySelectorAll('.mail-checkbox');
  
  checkboxes.forEach(checkbox => {
    checkbox.checked = isChecked;
  });
  
  updateDeleteButtonVisibility();
}

function updateDeleteButtonVisibility() {
  const deleteSelectedBtn = document.getElementById('delete-selected-mail');
  const selectAllCheckbox = document.getElementById('select-all-mail');
  const checkboxes = document.querySelectorAll('.mail-checkbox');
  const selectedCount = document.querySelectorAll('.mail-checkbox:checked').length;
  
  // Mostrar/ocultar botão de apagar
  deleteSelectedBtn.classList.toggle('hidden', selectedCount === 0);
  
  // Atualizar estado do checkbox "selecionar todos"
  if (selectedCount === 0) {
    selectAllCheckbox.indeterminate = false;
    selectAllCheckbox.checked = false;
  } else if (selectedCount === checkboxes.length) {
    selectAllCheckbox.indeterminate = false;
    selectAllCheckbox.checked = true;
  } else {
    selectAllCheckbox.indeterminate = true;
    selectAllCheckbox.checked = false;
  }
}

function handleDeleteSelected() {
  const selectedIds = [...document.querySelectorAll('.mail-checkbox:checked')].map(cb => cb.dataset.id);
  if (selectedIds.length === 0) return;
  if (confirm(`Tem a certeza que deseja apagar os ${selectedIds.length} registos selecionados?`)) {
    selectedIds.forEach(id => deleteRecord('mail', id));
    loadMailData();
  }
}

function handleExportMail() {
  const mails = getAllRecords('mail');
  if (mails.length === 0) {
    alert('Não existem dados para exportar.');
    return;
  }
  downloadCSV(convertArrayOfObjectsToCSV(mails), `registo_correio_${new Date().toISOString().split('T')[0]}.csv`);
}

function handleScan() {
  showSection('scanner-content');
  setTimeout(() => {
    document.getElementById('start-scanner')?.click();
    window.onScanSuccess = (decodedText) => {
      document.getElementById('mail-code').value = decodedText.split(':').pop();
      showSection('mail-content');
      window.onScanSuccess = null;
    };
  }, 100);
}
