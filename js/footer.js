// Footer module JavaScript

let sessionStartTime = new Date();

function initFooterModule() {
  setupFooterEventListeners();
  updateFooterStats();
  updateSystemInfo();
  
  // Atualizar ano atual
  const currentYear = new Date().getFullYear();
  document.getElementById('current-year').textContent = currentYear;
  document.getElementById('copyright-year').textContent = currentYear;
}

function setupFooterEventListeners() {
  // Event listeners para os botões do footer
  const exportAllBtn = document.getElementById('export-all-data');
  const systemInfoBtn = document.getElementById('system-info');
  const closeSystemInfoBtn = document.getElementById('close-system-info');
  const systemInfoModal = document.getElementById('system-info-modal');

  if (exportAllBtn) {
    exportAllBtn.addEventListener('click', handleExportAllData);
  }

  if (systemInfoBtn) {
    systemInfoBtn.addEventListener('click', () => {
      updateSystemInfo();
      systemInfoModal.classList.remove('hidden');
    });
  }

  if (closeSystemInfoBtn) {
    closeSystemInfoBtn.addEventListener('click', () => {
      systemInfoModal.classList.add('hidden');
    });
  }

  // Fechar modal ao clicar fora
  if (systemInfoModal) {
    systemInfoModal.addEventListener('click', (e) => {
      if (e.target === systemInfoModal) {
        systemInfoModal.classList.add('hidden');
      }
    });
  }

  // Event listeners para os links rápidos do footer
  const footerLinks = document.querySelectorAll('footer [data-section]');
  footerLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      const sectionId = link.dataset.section;
      if (typeof showSection === 'function') {
        showSection(sectionId);
      }
    });
  });
}

function updateFooterStats() {
  const exams = JSON.parse(localStorage.getItem('hospitalExams')) || [];
  const mail = JSON.parse(localStorage.getItem('hospitalMail')) || [];
  const users = JSON.parse(localStorage.getItem('hospitalUsers')) || [];
  const activities = JSON.parse(localStorage.getItem('activityLogs')) || [];

  // Atualizar contadores no footer
  const footerExams = document.getElementById('footer-total-exams');
  const footerMail = document.getElementById('footer-total-mail');
  const footerUsers = document.getElementById('footer-total-users');
  const footerLastActivity = document.getElementById('footer-last-activity');

  if (footerExams) footerExams.textContent = exams.length;
  if (footerMail) footerMail.textContent = mail.length;
  if (footerUsers) footerUsers.textContent = users.length;

  // Última atividade
  if (footerLastActivity && activities.length > 0) {
    const lastActivity = activities[0];
    const timeDiff = Date.now() - new Date(lastActivity.timestamp).getTime();
    const minutes = Math.floor(timeDiff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    let timeText;
    if (minutes < 1) timeText = 'Agora';
    else if (minutes < 60) timeText = `${minutes}m`;
    else if (hours < 24) timeText = `${hours}h`;
    else timeText = `${days}d`;

    footerLastActivity.textContent = timeText;
  } else if (footerLastActivity) {
    footerLastActivity.textContent = '-';
  }
}

function updateSystemInfo() {
  const lastUpdateEl = document.getElementById('last-update');
  const browserInfoEl = document.getElementById('browser-info');
  const storageInfoEl = document.getElementById('storage-info');
  const sessionTimeEl = document.getElementById('session-time');

  // Última atualização (simulada)
  if (lastUpdateEl) {
    lastUpdateEl.textContent = new Date().toLocaleDateString('pt-PT');
  }

  // Informações do navegador
  if (browserInfoEl) {
    const userAgent = navigator.userAgent;
    let browserName = 'Desconhecido';
    
    if (userAgent.includes('Chrome')) browserName = 'Chrome';
    else if (userAgent.includes('Firefox')) browserName = 'Firefox';
    else if (userAgent.includes('Safari')) browserName = 'Safari';
    else if (userAgent.includes('Edge')) browserName = 'Edge';
    
    browserInfoEl.textContent = browserName;
  }

  // Informações de armazenamento
  if (storageInfoEl) {
    try {
      let totalSize = 0;
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          totalSize += localStorage[key].length;
        }
      }
      const sizeInKB = (totalSize / 1024).toFixed(2);
      storageInfoEl.textContent = `${sizeInKB} KB`;
    } catch (e) {
      storageInfoEl.textContent = 'N/A';
    }
  }

  // Tempo de sessão
  if (sessionTimeEl) {
    const sessionDuration = Date.now() - sessionStartTime.getTime();
    const minutes = Math.floor(sessionDuration / 60000);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      sessionTimeEl.textContent = `${hours}h ${minutes % 60}m`;
    } else {
      sessionTimeEl.textContent = `${minutes}m`;
    }
  }
}

function handleExportAllData() {
  try {
    const allData = {
      exams: JSON.parse(localStorage.getItem('hospitalExams')) || [],
      mail: JSON.parse(localStorage.getItem('hospitalMail')) || [],
      users: JSON.parse(localStorage.getItem('hospitalUsers')) || [],
      activities: JSON.parse(localStorage.getItem('activityLogs')) || [],
      exportDate: new Date().toISOString(),
      version: '2.0.0'
    };

    const dataStr = JSON.stringify(allData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `backup_sistema_bgs_${new Date().toISOString().split('T')[0]}.json`;
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Adicionar log de atividade
    if (typeof addActivityLog === 'function') {
      addActivityLog('Exportação de Dados', 'Backup completo do sistema');
    }

    // Mostrar notificação de sucesso
    showNotification('Dados exportados com sucesso!', 'success');
  } catch (error) {
    console.error('Erro ao exportar dados:', error);
    showNotification('Erro ao exportar dados', 'error');
  }
}

function showNotification(message, type = 'info') {
  // Criar notificação temporária
  const notification = document.createElement('div');
  notification.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg transition-all duration-300 ${
    type === 'success' ? 'bg-green-500 text-white' :
    type === 'error' ? 'bg-red-500 text-white' :
    'bg-blue-500 text-white'
  }`;
  notification.innerHTML = `
    <div class="flex items-center">
      <i class="fas ${type === 'success' ? 'fa-check' : type === 'error' ? 'fa-exclamation-triangle' : 'fa-info'} mr-2"></i>
      <span>${message}</span>
    </div>
  `;

  document.body.appendChild(notification);

  // Remover após 3 segundos
  setTimeout(() => {
    notification.style.opacity = '0';
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 3000);
}

// Função para atualizar estatísticas do footer quando há mudanças
function updateFooterData() {
  updateFooterStats();
}

// Atualizar informações do sistema periodicamente
setInterval(updateSystemInfo, 60000); // A cada minuto

// Inicializar quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
  initFooterModule();
  
  // Atualizar estatísticas periodicamente
  setInterval(updateFooterStats, 30000); // A cada 30 segundos
});

// Exportar funções para uso global
window.updateFooterData = updateFooterData;
window.showNotification = showNotification;
