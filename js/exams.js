// Exams module JavaScript

function initExamsModule() {
  // --- Element Cache ---
  const examForm = document.getElementById('exam-form');
  if (!examForm) return; // Exit if not on the right page

  const examTableBody = document.getElementById('exams-table-body');
  const searchInput = document.getElementById('search-exams');
  const selectAllCheckbox = document.getElementById('select-all-exams');
  const deleteSelectedBtn = document.getElementById('delete-selected-exams');

  const exportExamsBtn = document.getElementById('export-exams');
  const printExamsBtn = document.getElementById('print-exams');
  const scanPatientBtn = document.getElementById('scan-patient');
  const qrCodeModal = document.getElementById('qr-code-modal');
  const closeQrModalBtn = document.getElementById('close-qr-modal');

  // Verificar se os elementos existem antes de adicionar event listeners
  if (!examTableBody || !searchInput || !selectAllCheckbox || !deleteSelectedBtn) {
    console.error('Elementos essenciais não encontrados para o módulo de exames');
    return;
  }

  // --- Event Listeners ---
  examForm.addEventListener('submit', handleExamSubmit);
  searchInput.addEventListener('input', () => loadExamsData());
  selectAllCheckbox.addEventListener('change', handleSelectAll);
  deleteSelectedBtn.addEventListener('click', handleDeleteSelected);

  examTableBody.addEventListener('change', (e) => {
    if (e.target.classList.contains('exam-checkbox')) {
      // Adicionar feedback visual quando um checkbox é selecionado
      const row = e.target.closest('tr');
      if (row) {
        if (e.target.checked) {
          row.classList.add('bg-blue-50');
        } else {
          row.classList.remove('bg-blue-50');
        }
      }
      updateDeleteButtonVisibility();
    }
  });

  examTableBody.addEventListener('click', (e) => {
    const button = e.target.closest('button');
    if (!button) return;
    const { action, id } = button.dataset;
    if (action === 'edit') handleEditExam(id);
    if (action === 'delete') handleDeleteExam(id);
  });

  if (scanPatientBtn) scanPatientBtn.addEventListener('click', handleScan);
  if (exportExamsBtn) exportExamsBtn.addEventListener('click', handleExportExams);
  if (printExamsBtn) printExamsBtn.addEventListener('click', () => printTable('exams-table'));
  if (closeQrModalBtn) closeQrModalBtn.addEventListener('click', () => qrCodeModal.classList.add('hidden'));

  // Adicionar atalhos de teclado para melhor usabilidade
  document.addEventListener('keydown', (e) => {
    // Verificar se estamos na secção de exames
    const examsSection = document.getElementById('exams-content');
    if (!examsSection || examsSection.classList.contains('hidden')) return;

    // Ctrl+A para selecionar todos
    if (e.ctrlKey && e.key === 'a') {
      e.preventDefault();
      selectAllCheckbox.checked = !selectAllCheckbox.checked;
      handleSelectAll({ target: selectAllCheckbox });
    }

    // Delete para apagar selecionados
    if (e.key === 'Delete') {
      const selectedCount = document.querySelectorAll('.exam-checkbox:checked').length;
      if (selectedCount > 0) {
        e.preventDefault();
        handleDeleteSelected();
      }
    }
  });

  // --- Initial Load ---
  loadExamsData();
  if (!document.getElementById('exam-id-editing')) {
    const hiddenInput = document.createElement('input');
    hiddenInput.type = 'hidden';
    hiddenInput.id = 'exam-id-editing';
    examForm.appendChild(hiddenInput);
  }
}

function loadExamsData() {
  const examTableBody = document.getElementById('exams-table-body');
  const searchInput = document.getElementById('search-exams');
  const searchTerm = searchInput.value.toLowerCase();
  
  let exams = getAllRecords('exams');

  if (searchTerm) {
    exams = exams.filter(exam => 
      Object.values(exam).some(value => 
        String(value).toLowerCase().includes(searchTerm)
      )
    );
  }
  
  exams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
  
  examTableBody.innerHTML = '';
  if (exams.length === 0) {
    examTableBody.innerHTML = `<tr><td colspan="8" class="px-6 py-4 text-center text-gray-500">Nenhum exame encontrado</td></tr>`;
    // Resetar o estado do checkbox "selecionar todos" quando não há exames
    const selectAllCheckbox = document.getElementById('select-all-exams');
    if (selectAllCheckbox) {
      selectAllCheckbox.checked = false;
      selectAllCheckbox.indeterminate = false;
    }
    updateDeleteButtonVisibility();
    return;
  }
  
  exams.forEach(exam => {
    const row = document.createElement('tr');
    row.dataset.id = exam.id;

    // Criar células individualmente para melhor controlo
    const checkboxCell = document.createElement('td');
    checkboxCell.className = 'px-6 py-4 whitespace-nowrap text-sm font-medium';
    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.className = 'exam-checkbox rounded border-gray-300 text-blue-600';
    checkbox.dataset.id = exam.id;
    checkboxCell.appendChild(checkbox);
    row.appendChild(checkboxCell);

    // Adicionar outras células
    row.innerHTML += `
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${exam.patientId || ''}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${exam.patientName || ''}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${exam.examType || ''}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${exam.examDate || ''}</td>
      <td class="px-6 py-4 whitespace-nowrap">
        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${exam.examStatus === 'Agendado' ? 'bg-yellow-100 text-yellow-800' : exam.examStatus === 'Realizado' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
          ${exam.examStatus || 'Pendente'}
        </span>
      </td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${exam.registeredBy || ''}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <button data-action="edit" data-id="${exam.id}" class="text-blue-600 hover:text-blue-900 mr-3" title="Editar"><i class="fas fa-edit"></i></button>
        <button data-action="delete" data-id="${exam.id}" class="text-red-600 hover:text-red-900" title="Apagar"><i class="fas fa-trash"></i></button>
      </td>
    `;
    examTableBody.appendChild(row);
  });

  // Atualizar visibilidade do botão de apagar após carregar os dados
  updateDeleteButtonVisibility();
}

function handleExamSubmit(e) {
  e.preventDefault();
  const examData = {
    patientId: document.getElementById('patient-id').value,
    patientName: document.getElementById('patient-name').value,
    examType: document.getElementById('exam-type').value,
    examDate: document.getElementById('exam-date').value,
    examStatus: document.getElementById('exam-status').value,
    notes: document.getElementById('exam-notes').value,
  };

  if (!examData.patientId || !examData.patientName || !examData.examType || !examData.examDate) {
    alert('Por favor, preencha todos os campos obrigatórios.');
    return;
  }

  const editingId = document.getElementById('exam-id-editing').value;
  const savedRecord = editingId ? updateRecord('exams', editingId, examData) : saveRecord('exams', examData);

  if (savedRecord && !editingId) showQrCodeModal('exams', savedRecord.id);
  
  e.target.reset();
  document.getElementById('exam-id-editing').value = '';
  loadExamsData();
}

function handleEditExam(id) {
  const exam = getRecordById('exams', id);
  if (!exam) return;
  document.getElementById('patient-id').value = exam.patientId;
  document.getElementById('patient-name').value = exam.patientName;
  document.getElementById('exam-type').value = exam.examType;
  document.getElementById('exam-date').value = exam.examDate;
  document.getElementById('exam-status').value = exam.examStatus;
  document.getElementById('exam-notes').value = exam.notes;
  document.getElementById('exam-id-editing').value = id;
  document.getElementById('exam-form').scrollIntoView({ behavior: 'smooth' });
}

function handleDeleteExam(id) {
  if (confirm('Tem a certeza que deseja apagar este registo de exame?')) {
    if (deleteRecord('exams', id)) loadExamsData();
  }
}

function handleSelectAll(e) {
  const isChecked = e.target.checked;
  const checkboxes = document.querySelectorAll('.exam-checkbox');

  // Verificar se existem checkboxes antes de tentar selecioná-los
  if (checkboxes.length === 0) {
    console.warn('Nenhum checkbox de exame encontrado');
    return;
  }

  checkboxes.forEach(checkbox => {
    if (!checkbox.disabled) { // Apenas selecionar checkboxes que não estão desabilitados
      checkbox.checked = isChecked;

      // Aplicar feedback visual
      const row = checkbox.closest('tr');
      if (row) {
        if (isChecked) {
          row.classList.add('bg-blue-50');
        } else {
          row.classList.remove('bg-blue-50');
        }
      }
    }
  });

  updateDeleteButtonVisibility();
}

function updateDeleteButtonVisibility() {
  const deleteSelectedBtn = document.getElementById('delete-selected-exams');
  const selectAllCheckbox = document.getElementById('select-all-exams');
  const checkboxes = document.querySelectorAll('.exam-checkbox');
  const selectedCheckboxes = document.querySelectorAll('.exam-checkbox:checked');
  const selectedCount = selectedCheckboxes.length;

  // Verificar se os elementos existem
  if (!deleteSelectedBtn || !selectAllCheckbox) {
    console.warn('Elementos de controlo não encontrados');
    return;
  }

  // Mostrar/ocultar botão de apagar e atualizar texto
  if (selectedCount === 0) {
    deleteSelectedBtn.classList.add('hidden');
    deleteSelectedBtn.innerHTML = '<i class="fas fa-trash-alt mr-1"></i> Apagar';
  } else {
    deleteSelectedBtn.classList.remove('hidden');
    deleteSelectedBtn.innerHTML = `<i class="fas fa-trash-alt mr-1"></i> Apagar (${selectedCount})`;
  }

  // Atualizar estado do checkbox "selecionar todos"
  if (checkboxes.length === 0) {
    // Não há checkboxes, resetar o estado
    selectAllCheckbox.indeterminate = false;
    selectAllCheckbox.checked = false;
  } else if (selectedCount === 0) {
    // Nenhum selecionado
    selectAllCheckbox.indeterminate = false;
    selectAllCheckbox.checked = false;
  } else if (selectedCount === checkboxes.length) {
    // Todos selecionados
    selectAllCheckbox.indeterminate = false;
    selectAllCheckbox.checked = true;
  } else {
    // Alguns selecionados
    selectAllCheckbox.indeterminate = true;
    selectAllCheckbox.checked = false;
  }
}

function handleDeleteSelected() {
  const selectedCheckboxes = document.querySelectorAll('.exam-checkbox:checked');
  const selectedIds = [...selectedCheckboxes].map(cb => cb.dataset.id).filter(id => id); // Filtrar IDs válidos

  if (selectedIds.length === 0) {
    alert('Nenhum exame selecionado para apagar.');
    return;
  }

  const confirmMessage = selectedIds.length === 1
    ? 'Tem a certeza que deseja apagar o registo selecionado?'
    : `Tem a certeza que deseja apagar os ${selectedIds.length} registos selecionados?`;

  if (confirm(confirmMessage)) {
    let deletedCount = 0;
    selectedIds.forEach(id => {
      if (deleteRecord('exams', id)) {
        deletedCount++;
      }
    });

    if (deletedCount > 0) {
      loadExamsData();
      const successMessage = deletedCount === 1
        ? 'Registo apagado com sucesso.'
        : `${deletedCount} registos apagados com sucesso.`;
      console.log(successMessage);
    } else {
      alert('Erro ao apagar os registos selecionados.');
    }
  }
}

function handleExportExams() {
  const exams = getAllRecords('exams');
  if (exams.length === 0) {
    alert('Não existem dados para exportar.');
    return;
  }
  downloadCSV(convertArrayOfObjectsToCSV(exams), `registo_exames_${new Date().toISOString().split('T')[0]}.csv`);
}

function handleScan() {
  showSection('scanner-content');
  setTimeout(() => {
    document.getElementById('start-scanner')?.click();
    window.onScanSuccess = (decodedText) => {
      document.getElementById('patient-id').value = decodedText.split(':').pop();
      showSection('exams-content');
      window.onScanSuccess = null;
    };
  }, 100);
}
