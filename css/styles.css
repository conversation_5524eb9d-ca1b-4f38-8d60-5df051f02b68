.sidebar {
    transition: all 0.3s ease;
}
.sidebar.collapsed {
    width: 70px;
}
.sidebar.collapsed .sidebar-text {
    display: none;
}
.sidebar.collapsed .logo-text {
    display: none;
}
.main-content {
    transition: all 0.3s ease;
}
.sidebar.collapsed + .main-content {
    margin-left: 70px;
}
/* Scanner styles removidos - agora implementado na barra lateral */
.barcode-preview {
    height: 100px;
    margin: 10px 0;
}
.fade-in {
    animation: fadeIn 0.3s ease-in;
}
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
#qr-reader-container {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: #f9fafb;
}
.scanner-active {
    border: 2px solid #3b82f6;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

/* Estilos para arredondar os cantos do cabeçalho das tabelas */
thead.bg-gray-50 {
    border-radius: 8px;
}

thead.bg-gray-50 tr:first-child th:first-child {
    border-top-left-radius: 8px;
}

thead.bg-gray-50 tr:first-child th:last-child {
    border-top-right-radius: 8px;
}

/* Adicionar bordas arredondadas ao último elemento do tbody para completar o visual */
tbody tr:last-child td:first-child {
    border-bottom-left-radius: 8px;
}

tbody tr:last-child td:last-child {
    border-bottom-right-radius: 8px;
}