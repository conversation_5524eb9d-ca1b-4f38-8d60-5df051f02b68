.sidebar {
    transition: all 0.3s ease;
}
.sidebar.collapsed {
    width: 70px;
}
.sidebar.collapsed .sidebar-text {
    display: none;
}
.sidebar.collapsed .logo-text {
    display: none;
}
.main-content {
    transition: all 0.3s ease;
}
.sidebar.collapsed + .main-content {
    margin-left: 70px;
}
/* Scanner styles removidos - agora implementado na barra lateral */
.barcode-preview {
    height: 100px;
    margin: 10px 0;
}
.fade-in {
    animation: fadeIn 0.3s ease-in;
}
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
#qr-reader-container {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: #f9fafb;
}
.scanner-active {
    border: 2px solid #3b82f6;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

/* Estilos para arredondar os cantos do cabeçalho das tabelas */
thead.bg-gray-50 {
    border-radius: 8px;
}

thead.bg-gray-50 tr:first-child th:first-child {
    border-top-left-radius: 8px;
}

thead.bg-gray-50 tr:first-child th:last-child {
    border-top-right-radius: 8px;
}

/* Adicionar bordas arredondadas ao último elemento do tbody para completar o visual */
tbody tr:last-child td:first-child {
    border-bottom-left-radius: 8px;
}

tbody tr:last-child td:last-child {
    border-bottom-right-radius: 8px;
}

/* Dashboard specific styles */
.dashboard-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar for activity container */
#activity-container::-webkit-scrollbar {
    width: 8px;
}

#activity-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

#activity-container::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
}

#activity-container::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* Chart container styles */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Activity search input focus styles */
#activity-search:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Loading animation for charts */
.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6b7280;
}

.chart-loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Activity badge animations */
.activity-badge {
    transition: all 0.2s ease;
}

.activity-badge:hover {
    transform: scale(1.05);
}

/* Quick action buttons */
.quick-action-btn {
    transition: all 0.2s ease;
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Footer styles */
footer {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Notification styles */
.notification {
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .dashboard-card {
        padding: 1rem;
    }

    #activity-container {
        max-height: 300px;
    }

    .quick-action-btn {
        padding: 0.75rem;
    }

    .quick-action-btn .w-12 {
        width: 2.5rem;
        height: 2.5rem;
    }
}

/* Content section styles */
.content-section {
    display: block;
}

.content-section.hidden {
    display: none;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    body {
        font-size: 12pt;
    }

    table {
        border-collapse: collapse;
        width: 100%;
    }

    th, td {
        border: 1px solid #000;
        padding: 8px;
        text-align: left;
    }

    th {
        background-color: #f0f0f0;
    }

    /* Hide charts in print */
    canvas {
        display: none !important;
    }
}