ppartem varios ficheiros mais pequenos apa facilitar a mnutenção:<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Registos Hospitalares</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.sheetjs.com/xlsx-0.19.3/package/dist/xlsx.full.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="https://unpkg.com/html5-qrcode@2.3.8/dist/html5-qrcode.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .sidebar {
            transition: all 0.3s ease;
        }
        .sidebar.collapsed {
            width: 70px;
        }
        .sidebar.collapsed .sidebar-text {
            display: none;
        }
        .sidebar.collapsed .logo-text {
            display: none;
        }
        .main-content {
            transition: all 0.3s ease;
        }
        .sidebar.collapsed + .main-content {
            margin-left: 70px;
        }
        #scanner-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        #qr-reader {
            width: 500px;
            max-width: 90%;
        }
        .barcode-preview {
            height: 100px;
            margin: 10px 0;
        }
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div class="sidebar bg-blue-800 text-white w-64 flex flex-col">
            <div class="p-4 flex items-center justify-between border-b border-blue-700">
                <div class="flex items-center">
                    <i class="fas fa-hospital text-2xl mr-2"></i>
                    <span class="logo-text text-xl font-bold">HospitalSys</span>
                </div>
                <button id="toggle-sidebar" class="text-white focus:outline-none">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <div class="flex-1 overflow-y-auto">
                <nav class="p-4">
                    <div class="mb-6">
                        <p class="text-blue-300 uppercase text-xs font-semibold sidebar-text">Menu Principal</p>
                        <ul class="mt-2">
                            <li class="mb-1">
                                <a href="#" id="dashboard-link" class="flex items-center p-2 text-white rounded hover:bg-blue-700 transition">
                                    <i class="fas fa-tachometer-alt mr-3"></i>
                                    <span class="sidebar-text">Dashboard</span>
                                </a>
                            </li>
                            <li class="mb-1">
                                <a href="#" id="exams-link" class="flex items-center p-2 text-white rounded hover:bg-blue-700 transition">
                                    <i class="fas fa-flask mr-3"></i>
                                    <span class="sidebar-text">Registo de Exames</span>
                                </a>
                            </li>
                            <li class="mb-1">
                                <a href="#" id="mail-link" class="flex items-center p-2 text-white rounded hover:bg-blue-700 transition">
                                    <i class="fas fa-envelope mr-3"></i>
                                    <span class="sidebar-text">Correio Interno</span>
                                </a>
                            </li>
                            <li class="mb-1">
                                <a href="#" id="users-link" class="flex items-center p-2 text-white rounded hover:bg-blue-700 transition">
                                    <i class="fas fa-users mr-3"></i>
                                    <span class="sidebar-text">Gestão de Utilizadores</span>
                                </a>
                            </li>
                            <li class="mb-1">
                                <a href="#" id="reports-link" class="flex items-center p-2 text-white rounded hover:bg-blue-700 transition">
                                    <i class="fas fa-file-excel mr-3"></i>
                                    <span class="sidebar-text">Relatórios</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>
            </div>
            <div class="p-4 border-t border-blue-700">
                <div class="flex items-center">
                    <img src="https://ui-avatars.com/api/?name=Admin" alt="User" class="w-8 h-8 rounded-full">
                    <div class="ml-3 sidebar-text">
                        <p class="text-sm font-medium" id="current-user">Admin</p>
                        <p class="text-xs text-blue-300">Administrador</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content flex-1 overflow-y-auto">
            <!-- Top Navigation -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between p-4">
                    <h1 class="text-2xl font-semibold text-gray-800" id="page-title">Dashboard</h1>
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <input type="text" placeholder="Pesquisar..." class="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        <button class="p-2 text-gray-600 hover:text-blue-600 focus:outline-none">
                            <i class="fas fa-bell"></i>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="p-6">
                <!-- Dashboard Content -->
                <div id="dashboard-content" class="content-section">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <div class="bg-white p-6 rounded-lg shadow">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500">Exames Registados</p>
                                    <h3 class="text-2xl font-bold" id="total-exams">0</h3>
                                </div>
                                <div class="bg-blue-100 p-3 rounded-full">
                                    <i class="fas fa-flask text-blue-600 text-xl"></i>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white p-6 rounded-lg shadow">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500">Correspondência</p>
                                    <h3 class="text-2xl font-bold" id="total-mail">0</h3>
                                </div>
                                <div class="bg-green-100 p-3 rounded-full">
                                    <i class="fas fa-envelope text-green-600 text-xl"></i>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white p-6 rounded-lg shadow">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500">Utilizadores</p>
                                    <h3 class="text-2xl font-bold" id="total-users">1</h3>
                                </div>
                                <div class="bg-purple-100 p-3 rounded-full">
                                    <i class="fas fa-users text-purple-600 text-xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow mb-6">
                        <h2 class="text-xl font-semibold mb-4">Atividade Recente</h2>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data/Hora</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utilizador</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ação</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Detalhes</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200" id="recent-activity">
                                    <!-- Activity logs will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Exams Registration Content -->
                <div id="exams-content" class="content-section hidden">
                    <div class="bg-white p-6 rounded-lg shadow mb-6">
                        <h2 class="text-xl font-semibold mb-4">Registo de Exames</h2>
                        <form id="exam-form" class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="patient-id" class="block text-sm font-medium text-gray-700">Nº Utente</label>
                                    <div class="mt-1 flex rounded-md shadow-sm">
                                        <input type="text" id="patient-id" class="focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-md sm:text-sm border-gray-300">
                                        <button type="button" id="scan-patient" class="ml-2 inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                            <i class="fas fa-barcode mr-1"></i> Ler
                                        </button>
                                    </div>
                                </div>
                                <div>
                                    <label for="patient-name" class="block text-sm font-medium text-gray-700">Nome do Utente</label>
                                    <input type="text" id="patient-name" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label for="exam-type" class="block text-sm font-medium text-gray-700">Tipo de Exame</label>
                                    <select id="exam-type" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">Selecione...</option>
                                        <option value="Hemograma">Hemograma</option>
                                        <option value="Bioquímica">Bioquímica</option>
                                        <option value="Urina">Urina</option>
                                        <option value="Raio-X">Raio-X</option>
                                        <option value="Ressonância">Ressonância</option>
                                        <option value="Ecografia">Ecografia</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="exam-date" class="block text-sm font-medium text-gray-700">Data do Exame</label>
                                    <input type="date" id="exam-date" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>
                                <div>
                                    <label for="exam-status" class="block text-sm font-medium text-gray-700">Estado</label>
                                    <select id="exam-status" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="Agendado">Agendado</option>
                                        <option value="Realizado">Realizado</option>
                                        <option value="Cancelado">Cancelado</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div>
                                <label for="exam-notes" class="block text-sm font-medium text-gray-700">Observações</label>
                                <textarea id="exam-notes" rows="3" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"></textarea>
                            </div>
                            
                            <div class="flex justify-end space-x-3">
                                <button type="reset" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    Limpar
                                </button>
                                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-save mr-2"></i> Guardar
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="bg-white p-6 rounded-lg shadow">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-xl font-semibold">Exames Registados</h2>
                            <div class="flex space-x-2">
                                <button id="export-exams" class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-file-excel mr-1"></i> Exportar
                                </button>
                                <button id="print-exams" class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-print mr-1"></i> Imprimir
                                </button>
                            </div>
                        </div>
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nº Utente</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nome</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo de Exame</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estado</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registado por</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200" id="exams-table-body">
                                    <!-- Exams data will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Internal Mail Content -->
                <div id="mail-content" class="content-section hidden">
                    <div class="bg-white p-6 rounded-lg shadow mb-6">
                        <h2 class="text-xl font-semibold mb-4">Registo de Correio Interno</h2>
                        <form id="mail-form" class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="mail-code" class="block text-sm font-medium text-gray-700">Código</label>
                                    <div class="mt-1 flex rounded-md shadow-sm">
                                        <input type="text" id="mail-code" class="focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-md sm:text-sm border-gray-300">
                                        <button type="button" id="scan-mail" class="ml-2 inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                            <i class="fas fa-barcode mr-1"></i> Ler
                                        </button>
                                        <button type="button" id="generate-barcode" class="ml-2 inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                            <i class="fas fa-qrcode mr-1"></i> Gerar
                                        </button>
                                    </div>
                                </div>
                                <div id="barcode-preview-container" class="hidden">
                                    <label class="block text-sm font-medium text-gray-700">Pré-visualização</label>
                                    <div class="barcode-preview flex justify-center items-center bg-white p-2 rounded border">
                                        <svg id="barcode-preview-svg"></svg>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="mail-sender" class="block text-sm font-medium text-gray-700">Remetente</label>
                                    <input type="text" id="mail-sender" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>
                                <div>
                                    <label for="mail-recipient" class="block text-sm font-medium text-gray-700">Destinatário</label>
                                    <input type="text" id="mail-recipient" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label for="mail-type" class="block text-sm font-medium text-gray-700">Tipo</label>
                                    <select id="mail-type" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">Selecione...</option>
                                        <option value="Documento">Documento</option>
                                        <option value="Amostra">Amostra</option>
                                        <option value="Equipamento">Equipamento</option>
                                        <option value="Outro">Outro</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="mail-date" class="block text-sm font-medium text-gray-700">Data</label>
                                    <input type="date" id="mail-date" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>
                                <div>
                                    <label for="mail-status" class="block text-sm font-medium text-gray-700">Estado</label>
                                    <select id="mail-status" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="Registado">Registado</option>
                                        <option value="Entregue">Entregue</option>
                                        <option value="Pendente">Pendente</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div>
                                <label for="mail-description" class="block text-sm font-medium text-gray-700">Descrição</label>
                                <textarea id="mail-description" rows="3" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"></textarea>
                            </div>
                            
                            <div class="flex justify-end space-x-3">
                                <button type="reset" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    Limpar
                                </button>
                                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-save mr-2"></i> Guardar
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="bg-white p-6 rounded-lg shadow">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-xl font-semibold">Correio Registado</h2>
                            <div class="flex space-x-2">
                                <button id="export-mail" class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-file-excel mr-1"></i> Exportar
                                </button>
                                <button id="print-mail" class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-print mr-1"></i> Imprimir
                                </button>
                            </div>
                        </div>
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Código</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remetente</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Destinatário</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estado</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registado por</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200" id="mail-table-body">
                                    <!-- Mail data will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Users Management Content -->
                <div id="users-content" class="content-section hidden">
                    <div class="bg-white p-6 rounded-lg shadow mb-6">
                        <h2 class="text-xl font-semibold mb-4">Gestão de Utilizadores</h2>
                        <form id="user-form" class="space-y-4">
                            <input type="hidden" id="user-id">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="user-name" class="block text-sm font-medium text-gray-700">Nome Completo</label>
                                    <input type="text" id="user-name" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>
                                <div>
                                    <label for="user-email" class="block text-sm font-medium text-gray-700">Email</label>
                                    <input type="email" id="user-email" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label for="user-username" class="block text-sm font-medium text-gray-700">Nome de Utilizador</label>
                                    <input type="text" id="user-username" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>
                                <div>
                                    <label for="user-password" class="block text-sm font-medium text-gray-700">Palavra-passe</label>
                                    <input type="password" id="user-password" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>
                                <div>
                                    <label for="user-role" class="block text-sm font-medium text-gray-700">Perfil</label>
                                    <select id="user-role" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="Administrador">Administrador</option>
                                        <option value="Médico">Médico</option>
                                        <option value="Técnico">Técnico</option>
                                        <option value="Rececionista">Rececionista</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="flex justify-end space-x-3">
                                <button type="reset" id="reset-user-form" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    Limpar
                                </button>
                                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-save mr-2"></i> Guardar
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="bg-white p-6 rounded-lg shadow">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-xl font-semibold">Lista de Utilizadores</h2>
                            <div class="flex space-x-2">
                                <button id="export-users" class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-file-excel mr-1"></i> Exportar
                                </button>
                            </div>
                        </div>
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nome</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nome de Utilizador</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Perfil</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estado</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200" id="users-table-body">
                                    <!-- Users data will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Reports Content -->
                <div id="reports-content" class="content-section hidden">
                    <div class="bg-white p-6 rounded-lg shadow">
                        <h2 class="text-xl font-semibold mb-4">Relatórios</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div class="bg-white p-4 border rounded-lg">
                                <h3 class="text-lg font-medium mb-3">Relatório de Exames</h3>
                                <form id="exam-report-form" class="space-y-4">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label for="exam-report-start" class="block text-sm font-medium text-gray-700">Data Inicial</label>
                                            <input type="date" id="exam-report-start" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                        </div>
                                        <div>
                                            <label for="exam-report-end" class="block text-sm font-medium text-gray-700">Data Final</label>
                                            <input type="date" id="exam-report-end" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label for="exam-report-type" class="block text-sm font-medium text-gray-700">Tipo de Exame</label>
                                        <select id="exam-report-type" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            <option value="">Todos</option>
                                            <option value="Hemograma">Hemograma</option>
                                            <option value="Bioquímica">Bioquímica</option>
                                            <option value="Urina">Urina</option>
                                            <option value="Raio-X">Raio-X</option>
                                            <option value="Ressonância">Ressonância</option>
                                            <option value="Ecografia">Ecografia</option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label for="exam-report-status" class="block text-sm font-medium text-gray-700">Estado</label>
                                        <select id="exam-report-status" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            <option value="">Todos</option>
                                            <option value="Agendado">Agendado</option>
                                            <option value="Realizado">Realizado</option>
                                            <option value="Cancelado">Cancelado</option>
                                        </select>
                                    </div>
                                    
                                    <div class="flex justify-end">
                                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                            <i class="fas fa-file-excel mr-2"></i> Gerar Relatório
                                        </button>
                                    </div>
                                </form>
                            </div>
                            
                            <div class="bg-white p-4 border rounded-lg">
                                <h3 class="text-lg font-medium mb-3">Relatório de Correio</h3>
                                <form id="mail-report-form" class="space-y-4">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label for="mail-report-start" class="block text-sm font-medium text-gray-700">Data Inicial</label>
                                            <input type="date" id="mail-report-start" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                        </div>
                                        <div>
                                            <label for="mail-report-end" class="block text-sm font-medium text-gray-700">Data Final</label>
                                            <input type="date" id="mail-report-end" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label for="mail-report-type" class="block text-sm font-medium text-gray-700">Tipo</label>
                                        <select id="mail-report-type" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            <option value="">Todos</option>
                                            <option value="Documento">Documento</option>
                                            <option value="Amostra">Amostra</option>
                                            <option value="Equipamento">Equipamento</option>
                                            <option value="Outro">Outro</option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label for="mail-report-status" class="block text-sm font-medium text-gray-700">Estado</label>
                                        <select id="mail-report-status" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            <option value="">Todos</option>
                                            <option value="Registado">Registado</option>
                                            <option value="Entregue">Entregue</option>
                                            <option value="Pendente">Pendente</option>
                                        </select>
                                    </div>
                                    
                                    <div class="flex justify-end">
                                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                            <i class="fas fa-file-excel mr-2"></i> Gerar Relatório
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <div class="bg-white p-4 border rounded-lg">
                            <h3 class="text-lg font-medium mb-3">Relatório de Atividades</h3>
                            <form id="activity-report-form" class="space-y-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="activity-report-start" class="block text-sm font-medium text-gray-700">Data Inicial</label>
                                        <input type="date" id="activity-report-start" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                    </div>
                                    <div>
                                        <label for="activity-report-end" class="block text-sm font-medium text-gray-700">Data Final</label>
                                        <input type="date" id="activity-report-end" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                    </div>
                                </div>
                                
                                <div>
                                    <label for="activity-report-user" class="block text-sm font-medium text-gray-700">Utilizador</label>
                                    <select id="activity-report-user" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">Todos</option>
                                        <!-- Users will be populated here -->
                                    </select>
                                </div>
                                
                                <div class="flex justify-end">
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        <i class="fas fa-file-excel mr-2"></i> Gerar Relatório
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Barcode/QR Code Scanner Modal -->
    <div id="scanner-container" class="hidden">
        <div class="bg-white p-4 rounded-lg shadow-lg w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">Leitor de Código de Barras/QR</h3>
                <button id="close-scanner" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="qr-reader"></div>
            <div class="mt-4">
                <p class="text-sm text-gray-600">Aponte a câmara para o código de barras ou QR code</p>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmation-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <div class="flex items-center mb-4">
                <div class="bg-red-100 p-3 rounded-full mr-3">
                    <i class="fas fa-exclamation-circle text-red-600"></i>
                </div>
                <h3 class="text-lg font-semibold" id="confirmation-title">Confirmar ação</h3>
            </div>
            <p class="text-gray-600 mb-6" id="confirmation-message">Tem a certeza que deseja prosseguir com esta ação?</p>
            <div class="flex justify-end space-x-3">
                <button id="cancel-action" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    Cancelar
                </button>
                <button id="confirm-action" class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700">
                    Confirmar
                </button>
            </div>
        </div>
    </div>

    <script>
        // Application state
        const state = {
            currentUser: {
                id: 1,
                name: "Admin",
                username: "admin",
                role: "Administrador"
            },
            exams: [],
            mail: [],
            users: [
                {
                    id: 1,
                    name: "Admin",
                    username: "admin",
                    email: "<EMAIL>",
                    role: "Administrador",
                    active: true
                }
            ],
            activityLogs: [],
            currentScannerType: null,
            actionToConfirm: null,
            actionData: null
        };
        // DOM Elements
        const elements = {
            toggleSidebar: document.getElementById('toggle-sidebar'),
            sidebar: document.querySelector('.sidebar'),
            mainContent: document.querySelector('.main-content'),
            pageTitle: document.getElementById('page-title'),
            currentUser: document.getElementById('current-user'),
            contentSections: document.querySelectorAll('.content-section'),
            navLinks: {
                dashboard: document.getElementById('dashboard-link'),
                exams: document.getElementById('exams-link'),
                mail: document.getElementById('mail-link'),
                users: document.getElementById('users-link'),
                reports: document.getElementById('reports-link')
            },
            dashboard: {
                totalExams: document.getElementById('total-exams'),
                totalMail: document.getElementById('total-mail'),
                totalUsers: document.getElementById('total-users'),
                recentActivity: document.getElementById('recent-activity')
            },
            exams: {
                form: document.getElementById('exam-form'),
                tableBody: document.getElementById('exams-table-body'),
                exportBtn: document.getElementById('export-exams'),
                printBtn: document.getElementById('print-exams'),
                scanPatientBtn: document.getElementById('scan-patient')
            },
            mail: {
                form: document.getElementById('mail-form'),
                tableBody: document.getElementById('mail-table-body'),
                exportBtn: document.getElementById('export-mail'),
                printBtn: document.getElementById('print-mail'),
                scanMailBtn: document.getElementById('scan-mail'),
                generateBarcodeBtn: document.getElementById('generate-barcode'),
                barcodePreviewContainer: document.getElementById('barcode-preview-container'),
                barcodePreviewSvg: document.getElementById('barcode-preview-svg')
            },
            users: {
                form: document.getElementById('user-form'),
                tableBody: document.getElementById('users-table-body'),
                exportBtn: document.getElementById('export-users'),
                resetBtn: document.getElementById('reset-user-form')
            },
            reports: {
                examForm: document.getElementById('exam-report-form'),
                mailForm: document.getElementById('mail-report-form'),
                activityForm: document.getElementById('activity-report-form'),
                userSelect: document.getElementById('activity-report-user')
            },
            scanner: {
                container: document.getElementById('scanner-container'),
                closeBtn: document.getElementById('close-scanner'),
                qrReader: document.getElementById('qr-reader')
            },
            confirmationModal: {
                container: document.getElementById('confirmation-modal'),
                title: document.getElementById('confirmation-title'),
                message: document.getElementById('confirmation-message'),
                cancelBtn: document.getElementById('cancel-action'),
                confirmBtn: document.getElementById('confirm-action')
            }
        };
        // Initialize the application
        function init() {
            // Load sample data
            loadSampleData();
            
            // Set current user
            elements.currentUser.textContent = state.currentUser.name;
            
            // Initialize UI
            updateDashboard();
            renderExamsTable();
            renderMailTable();
            renderUsersTable();
            renderActivityLogs();
            
            // Set up event listeners
            setupEventListeners();
            
            // Show dashboard by default
            showSection('dashboard-content');
        }
        // Load sample data
        function loadSampleData() {
            // Sample exams
            state.exams = [
                {
                    id: 1,
                    patientId: 'UT12345',
                    patientName: 'João Silva',
                    examType: 'Hemograma',
                    examDate: '2023-06-15',
                    status: 'Realizado',
                    notes: 'Nenhuma observação relevante',
                    registeredBy: 'Admin',
                    registeredAt: '2023-06-10 09:15:23'
                },
                {
                    id: 2,
                    patientId: 'UT67890',
                    patientName: 'Maria Santos',
                    examType: 'Raio-X',
                    examDate: '2023-06-18',
                    status: 'Agendado',
                    notes: 'Raio-X torácico',
                    registeredBy: 'Admin',
                    registeredAt: '2023-06-12 14:30:45'
                }
            ];
            
            // Sample mail
            state.mail = [
                {
                    id: 1,
                    code: 'MAIL001',
                    sender: 'Laboratório Central',
                    recipient: 'Cardiologia',
                    type: 'Amostra',
                    date: '2023-06-10',
                    status: 'Entregue',
                    description: 'Amostras de sangue para análise',
                    registeredBy: 'Admin',
                    registeredAt: '2023-06-10 10:20:15'
                },
                {
                    id: 2,
                    code: 'MAIL002',
                    sender: 'Administração',
                    recipient: 'Direção',
                    type: 'Documento',
                    date: '2023-06-12',
                    status: 'Registado',
                    description: 'Relatório mensal',
                    registeredBy: 'Admin',
                    registeredAt: '2023-06-12 16:45:30'
                }
            ];
            
            // Sample activity logs
            state.activityLogs = [
                {
                    id: 1,
                    timestamp: '2023-06-12 16:45:30',
                    user: 'Admin',
                    action: 'Registo de correio',
                    details: 'Código: MAIL002'
                },
                {
                    id: 2,
                    timestamp: '2023-06-12 14:30:45',
                    user: 'Admin',
                    action: 'Registo de exame',
                    details: 'Utente: UT67890'
                },
                {
                    id: 3,
                    timestamp: '2023-06-10 10:20:15',
                    user: 'Admin',
                    action: 'Registo de correio',
                    details: 'Código: MAIL001'
                },
                {
                    id: 4,
                    timestamp: '2023-06-10 09:15:23',
                    user: 'Admin',
                    action: 'Registo de exame',
                    details: 'Utente: UT12345'
                }
            ];
        }
        // Set up event listeners
        function setupEventListeners() {
            // Sidebar toggle
            elements.toggleSidebar.addEventListener('click', toggleSidebar);
            
            // Navigation links
            elements.navLinks.dashboard.addEventListener('click', () => showSection('dashboard-content'));
            elements.navLinks.exams.addEventListener('click', () => showSection('exams-content'));
            elements.navLinks.mail.addEventListener('click', () => showSection('mail-content'));
            elements.navLinks.users.addEventListener('click', () => showSection('users-content'));
            elements.navLinks.reports.addEventListener('click', () => showSection('reports-content'));
            
            // Exams form
            elements.exams.form.addEventListener('submit', handleExamSubmit);
            elements.exams.exportBtn.addEventListener('click', exportExams);
            elements.exams.printBtn.addEventListener('click', printExams);
            elements.exams.scanPatientBtn.addEventListener('click', () => openScanner('patient'));
            
            // Mail form
            elements.mail.form.addEventListener('submit', handleMailSubmit);
            elements.mail.exportBtn.addEventListener('click', exportMail);
            elements.mail.printBtn.addEventListener('click', printMail);
            elements.mail.scanMailBtn.addEventListener('click', () => openScanner('mail'));
            elements.mail.generateBarcodeBtn.addEventListener('click', generateBarcode);
            
            // Users form
            elements.users.form.addEventListener('submit', handleUserSubmit);
            elements.users.exportBtn.addEventListener('click', exportUsers);
            elements.users.resetBtn.addEventListener('click', resetUserForm);
            
            // Reports forms
            elements.reports.examForm.addEventListener('submit', generateExamReport);
            elements.reports.mailForm.addEventListener('submit', generateMailReport);
            elements.reports.activityForm.addEventListener('submit', generateActivityReport);
            
            // Scanner
            elements.scanner.closeBtn.addEventListener('click', closeScanner);
            
            // Confirmation modal
            elements.confirmationModal.cancelBtn.addEventListener('click', closeConfirmationModal);
            elements.confirmationModal.confirmBtn.addEventListener('click', confirmAction);
        }
        // Toggle sidebar
        function toggleSidebar() {
            elements.sidebar.classList.toggle('collapsed');
        }
        // Show section
        function showSection(sectionId) {
            // Hide all sections
            elements.contentSections.forEach(section => {
                section.classList.add('hidden');
            });
            
            // Show the selected section
            document.getElementById(sectionId).classList.remove('hidden');
            
            // Update page title
            const titles = {
                'dashboard-content': 'Dashboard',
                'exams-content': 'Registo de Exames',
                'mail-content': 'Correio Interno',
                'users-content': 'Gestão de Utilizadores',
                'reports-content': 'Relatórios'
            };
            
            elements.pageTitle.textContent = titles[sectionId];
        }
        // Update dashboard
        function updateDashboard() {
            elements.dashboard.totalExams.textContent = state.exams.length;
            elements.dashboard.totalMail.textContent = state.mail.length;
            elements.dashboard.totalUsers.textContent = state.users.length;
        }
        // Render exams table
        function renderExamsTable() {
            const tableBody = elements.exams.tableBody;
            tableBody.innerHTML = '';
            
            state.exams.forEach(exam => {
                const row = document.createElement('tr');
                row.className = 'fade-in';
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${exam.patientId}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${exam.patientName}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${exam.examType}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${formatDate(exam.examDate)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(exam.status)}">
                            ${exam.status}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${exam.registeredBy}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button class="text-blue-600 hover:text-blue-900 mr-3 edit-exam" data-id="${exam.id}">Editar</button>
                        <button class="text-red-600 hover:text-red-900 delete-exam" data-id="${exam.id}">Eliminar</button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
            
            // Add event listeners to edit and delete buttons
            document.querySelectorAll('.edit-exam').forEach(btn => {
                btn.addEventListener('click', (e) => editExam(e.target.dataset.id));
            });
            
            document.querySelectorAll('.delete-exam').forEach(btn => {
                btn.addEventListener('click', (e) => confirmDelete('exam', e.target.dataset.id));
            });
        }
        // Render mail table
        function renderMailTable() {
            const tableBody = elements.mail.tableBody;
            tableBody.innerHTML = '';
            
            state.mail.forEach(item => {
                const row = document.createElement('tr');
                row.className = 'fade-in';
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${item.code}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.sender}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.recipient}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.type}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${formatDate(item.date)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(item.status)}">
                            ${item.status}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.registeredBy}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button class="text-blue-600 hover:text-blue-900 mr-3 edit-mail" data-id="${item.id}">Editar</button>
                        <button class="text-red-600 hover:text-red-900 delete-mail" data-id="${item.id}">Eliminar</button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
            
            // Add event listeners to edit and delete buttons
            document.querySelectorAll('.edit-mail').forEach(btn => {
                btn.addEventListener('click', (e) => editMail(e.target.dataset.id));
            });
            
            document.querySelectorAll('.delete-mail').forEach(btn => {
                btn.addEventListener('click', (e) => confirmDelete('mail', e.target.dataset.id));
            });
        }
        // Render users table
        function renderUsersTable() {
            const tableBody = elements.users.tableBody;
            tableBody.innerHTML = '';
            
            state.users.forEach(user => {
                const row = document.createElement('tr');
                row.className = 'fade-in';
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${user.name}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${user.username}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${user.email}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${user.role}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${user.active ? 'Ativo' : 'Inativo'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button class="text-blue-600 hover:text-blue-900 mr-3 edit-user" data-id="${user.id}">Editar</button>
                        <button class="text-red-600 hover:text-red-900 delete-user" data-id="${user.id}">${user.active ? 'Desativar' : 'Ativar'}</button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
            
            // Add event listeners to edit and delete buttons
            document.querySelectorAll('.edit-user').forEach(btn => {
                btn.addEventListener('click', (e) => editUser(e.target.dataset.id));
            });
            
            document.querySelectorAll('.delete-user').forEach(btn => {
                btn.addEventListener('click', (e) => toggleUserStatus(e.target.dataset.id));
            });
            
            // Populate user select in activity reports
            elements.reports.userSelect.innerHTML = '<option value="">Todos</option>';
            state.users.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = user.name;
                elements.reports.userSelect.appendChild(option);
            });
        }
        // Render activity logs
        function renderActivityLogs() {
            const tableBody = elements.dashboard.recentActivity;
            tableBody.innerHTML = '';
            
            // Show only the 5 most recent activities
            const recentActivities = [...state.activityLogs].sort((a, b) => 
                new Date(b.timestamp) - new Date(a.timestamp)
            ).slice(0, 5);
            
            recentActivities.forEach(log => {
                const row = document.createElement('tr');
                row.className = 'fade-in';
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${formatDateTime(log.timestamp)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${log.user}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${log.action}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${log.details}</td>
                `;
                tableBody.appendChild(row);
            });
        }
        // Handle exam form submission
        function handleExamSubmit(e) {
            e.preventDefault();
            
            const formData = {
                patientId: document.getElementById('patient-id').value,
                patientName: document.getElementById('patient-name').value,
                examType: document.getElementById('exam-type').value,
                examDate: document.getElementById('exam-date').value,
                status: document.getElementById('exam-status').value,
                notes: document.getElementById('exam-notes').value,
                registeredBy: state.currentUser.name,
                registeredAt: new Date().toISOString()
            };
            
            // Validate form
            if (!formData.patientId || !formData.patientName || !formData.examType || !formData.examDate) {
                alert('Por favor, preencha todos os campos obrigatórios.');
                return;
            }
            
            // Check if we're editing an existing exam
            const examId = document.getElementById('exam-id') ? parseInt(document.getElementById('exam-id').value) : null;
            
            if (examId) {
                // Update existing exam
                const index = state.exams.findIndex(exam => exam.id === examId);
                if (index !== -1) {
                    state.exams[index] = { ...state.exams[index], ...formData };
                    
                    // Log activity
                    logActivity('Atualização de exame', `Utente: ${formData.patientId}`);
                }
            } else {
                // Add new exam
                const newExam = {
                    id: state.exams.length > 0 ? Math.max(...state.exams.map(e => e.id)) + 1 : 1,
                    ...formData
                };
                
                state.exams.push(newExam);
                
                // Log activity
                logActivity('Registo de exame', `Utente: ${formData.patientId}`);
            }
            
            // Reset form
            e.target.reset();
            
            // Update UI
            renderExamsTable();
            updateDashboard();
            
            // Show success message
            alert('Exame registado com sucesso!');
        }
        // Handle mail form submission
        function handleMailSubmit(e) {
            e.preventDefault();
            
            const formData = {
                code: document.getElementById('mail-code').value,
                sender: document.getElementById('mail-sender').value,
                recipient: document.getElementById('mail-recipient').value,
                type: document.getElementById('mail-type').value,
                date: document.getElementById('mail-date').value,
                status: document.getElementById('mail-status').value,
                description: document.getElementById('mail-description').value,
                registeredBy: state.currentUser.name,
                registeredAt: new Date().toISOString()
            };
            
            // Validate form
            if (!formData.code || !formData.sender || !formData.recipient || !formData.type || !formData.date) {
                alert('Por favor, preencha todos os campos obrigatórios.');
                return;
            }
            
            // Check if we're editing an existing mail item
            const mailId = document.getElementById('mail-id') ? parseInt(document.getElementById('mail-id').value) : null;
            
            if (mailId) {
                // Update existing mail
                const index = state.mail.findIndex(item => item.id === mailId);
                if (index !== -1) {
                    state.mail[index] = { ...state.mail[index], ...formData };
                    
                    // Log activity
                    logActivity('Atualização de correio', `Código: ${formData.code}`);
                }
            } else {
                // Add new mail
                const newMail = {
                    id: state.mail.length > 0 ? Math.max(...state.mail.map(m => m.id)) + 1 : 1,
                    ...formData
                };
                
                state.mail.push(newMail);
                
                // Log activity
                logActivity('Registo de correio', `Código: ${formData.code}`);
            }
            
            // Reset form
            e.target.reset();
            elements.mail.barcodePreviewContainer.classList.add('hidden');
            
            // Update UI
            renderMailTable();
            updateDashboard();
            
            // Show success message
            alert('Correio registado com sucesso!');
        }
        // Handle user form submission
        function handleUserSubmit(e) {
            e.preventDefault();
            
            const formData = {
                name: document.getElementById('user-name').value,
                username: document.getElementById('user-username').value,
                email: document.getElementById('user-email').value,
                role: document.getElementById('user-role').value,
                active: true
            };
            
            const password = document.getElementById('user-password').value;
            
            // Validate form
            if (!formData.name || !formData.username || !formData.email || !formData.role) {
                alert('Por favor, preencha todos os campos obrigatórios.');
                return;
            }
            
            // Check if we're editing an existing user
            const userId = document.getElementById('user-id').value ? parseInt(document.getElementById('user-id').value) : null;
            
            if (userId) {
                // Update existing user
                const index = state.users.findIndex(user => user.id === userId);
                if (index !== -1) {
                    // Only update password if it was provided
                    const updatedUser = {
                        ...state.users[index],
                        ...formData
                    };
                    
                    if (password) {
                        // In a real app, you would hash the password here
                        updatedUser.password = password;
                    }
                    
                    state.users[index] = updatedUser;
                    
                    // Log activity
                    logActivity('Atualização de utilizador', `Nome: ${formData.name}`);
                }
            } else {
                // Check if password was provided for new users
                if (!password) {
                    alert('Por favor, defina uma palavra-passe para o novo utilizador.');
                    return;
                }
                
                // Add new user
                const newUser = {
                    id: state.users.length > 0 ? Math.max(...state.users.map(u => u.id)) + 1 : 1,
                    ...formData,
                    password: password // In a real app, you would hash this
                };
                
                state.users.push(newUser);
                
                // Log activity
                logActivity('Registo de utilizador', `Nome: ${formData.name}`);
            }
            
            // Reset form
            resetUserForm();
            
            // Update UI
            renderUsersTable();
            updateDashboard();
            
            // Show success message
            alert('Utilizador registado com sucesso!');
        }
        // Edit exam
        function editExam(id) {
            const exam = state.exams.find(e => e.id === parseInt(id));
            if (!exam) return;
            
            // Fill form with exam data
            document.getElementById('patient-id').value = exam.patientId;
            document.getElementById('patient-name').value = exam.patientName;
            document.getElementById('exam-type').value = exam.examType;
            document.getElementById('exam-date').value = exam.examDate;
            document.getElementById('exam-status').value = exam.status;
            document.getElementById('exam-notes').value = exam.notes;
            
            // Add hidden field for exam ID
            if (!document.getElementById('exam-id')) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.id = 'exam-id';
                input.value = exam.id;
                elements.exams.form.appendChild(input);
            } else {
                document.getElementById('exam-id').value = exam.id;
            }
            
            // Scroll to form
            document.getElementById('exam-form').scrollIntoView({ behavior: 'smooth' });
        }
        // Edit mail
        function editMail(id) {
            const mail = state.mail.find(m => m.id === parseInt(id));
            if (!mail) return;
            
            // Fill form with mail data
            document.getElementById('mail-code').value = mail.code;
            document.getElementById('mail-sender').value = mail.sender;
            document.getElementById('mail-recipient').value = mail.recipient;
            document.getElementById('mail-type').value = mail.type;
            document.getElementById('mail-date').value = mail.date;
            document.getElementById('mail-status').value = mail.status;
            document.getElementById('mail-description').value = mail.description;
            
            // Add hidden field for mail ID
            if (!document.getElementById('mail-id')) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.id = 'mail-id';
                input.value = mail.id;
                elements.mail.form.appendChild(input);
            } else {
                document.getElementById('mail-id').value = mail.id;
            }
            
            // Scroll to form
            document.getElementById('mail-form').scrollIntoView({ behavior: 'smooth' });
        }
        // Edit user
        function editUser(id) {
            const user = state.users.find(u => u.id === parseInt(id));
            if (!user) return;
            
            // Fill form with user data
            document.getElementById('user-id').value = user.id;
            document.getElementById('user-name').value = user.name;
            document.getElementById('user-username').value = user.username;
            document.getElementById('user-email').value = user.email;
            document.getElementById('user-role').value = user.role;
            
            // Clear password field
            document.getElementById('user-password').value = '';
            
            // Scroll to form
            document.getElementById('user-form').scrollIntoView({ behavior: 'smooth' });
        }
        // Reset user form
        function resetUserForm() {
            elements.users.form.reset();
            
            // Remove hidden ID field if it exists
            if (document.getElementById('user-id')) {
                elements.users.form.removeChild(document.getElementById('user-id'));
            }
        }
        // Toggle user status
        function toggleUserStatus(id) {
            const user = state.users.find(u => u.id === parseInt(id));
            if (!user) return;
            
            user.active = !user.active;
            
            // Log activity
            logActivity(
                user.active ? 'Ativação de utilizador' : 'Desativação de utilizador',
                `Nome: ${user.name}`
            );
            
            // Update UI
            renderUsersTable();
            updateDashboard();
            
            // Show success message
            alert(`Utilizador ${user.active ? 'ativado' : 'desativado'} com sucesso!`);
        }
        // Confirm delete action
        function confirmDelete(type, id) {
            let title, message;
            
            switch (type) {
                case 'exam':
                    const exam = state.exams.find(e => e.id === parseInt(id));
                    title = 'Eliminar Exame';
                    message = `Tem a certeza que deseja eliminar o exame do utente ${exam.patientName} (${exam.patientId})?`;
                    break;
                case 'mail':
                    const mail = state.mail.find(m => m.id === parseInt(id));
                    title = 'Eliminar Registo de Correio';
                    message = `Tem a certeza que deseja eliminar o registo de correio com código ${mail.code}?`;
                    break;
                default:
                    return;
            }
            
            // Set confirmation modal content
            elements.confirmationModal.title.textContent = title;
            elements.confirmationModal.message.textContent = message;
            
            // Set action to perform on confirmation
            state.actionToConfirm = `delete_${type}`;
            state.actionData = id;
            
            // Show modal
            elements.confirmationModal.container.classList.remove('hidden');
        }
        // Close confirmation modal
        function closeConfirmationModal() {
            elements.confirmationModal.container.classList.add('hidden');
            state.actionToConfirm = null;
            state.actionData = null;
        }
        // Confirm action
        function confirmAction() {
            if (!state.actionToConfirm || !state.actionData) {
                closeConfirmationModal();
                return;
            }
            
            const [action, type] = state.actionToConfirm.split('_');
            
            if (action === 'delete') {
                switch (type) {
                    case 'exam':
                        deleteExam(state.actionData);
                        break;
                    case 'mail':
                        deleteMail(state.actionData);
                        break;
                }
            }
            
            closeConfirmationModal();
        }
        // Delete exam
        function deleteExam(id) {
            const index = state.exams.findIndex(e => e.id === parseInt(id));
            if (index === -1) return;
            
            const exam = state.exams[index];
            
            // Remove exam
<p style="border-radius: 8px; text-align: center; font-size: 12px; color: #fff; margin-top: 16px;position: fixed; left: 8px; bottom: 8px; z-index: 10; background: rgba(0, 0, 0, 0.8); padding: 4px 8px;">Made with <img src="https://enzostvs-deepsite.hf.space/logo.svg" alt="DeepSite Logo" style="width: 16px; height: 16px; vertical-align: middle;display:inline-block;margin-right:3px;filter:brightness(0) invert(1);"><a href="https://enzostvs-deepsite.hf.space" style="color: #fff;text-decoration: underline;" target="_blank" >DeepSite</a> - 🧬 <a href="https://enzostvs-deepsite.hf.space?remix=Maddball/registo" style="color: #fff;text-decoration: underline;" target="_blank" >Remix</a></p></body>
</html>
I'll create a comprehensive hospital registration system based on your MVP requirements. This will be a single-page React application with all the core functionalities you've outlined.


V1
Hospital Registration System
hospital-registration-system.tsx
This hospital registration system provides a complete MVP with all the core functionalities you requested:

Key Features Implemented:
Dashboard: Shows key metrics (total exams, mail items, users) and recent activity logs
Exam Registration: Full CRUD operations for patient exams with status tracking
Mail Registration: Complete system for internal mail management with type/status tracking
User Management: User registration with role-based access and status toggling
Reports: Report generation interface with filters
Barcode Scanner: Modal placeholder for future scanner integration
Responsive Design: Collapsible sidebar that works on all screen sizes
Confirmation Modals: Safety checks for delete operations
Technical Implementation:
State Management: Uses React hooks for local state management
UI Components: Built with Shadcn UI components for consistent design
Icons: Uses Lucide React icons throughout
Responsive: Tailwind CSS for responsive design
Modular: Clean component structure for easy maintenance
Validation: Form validation for all inputs
Activity Logging: Tracks all system actions
The system is ready to use and can be easily extended with additional features like database integration, authentication, and more advanced reporting capabilities.

aplica lhe estes css:body {
	padding: 2rem;
	font-family: -apple-system, BlinkMacSystemFont, "Arial", sans-serif;
}

h1 {
	font-size: 16px;
	margin-top: 0;
}

p {
	color: rgb(107, 114, 128);
	font-size: 15px;
	margin-bottom: 10px;
	margin-top: 5px;
}

.card {
	max-width: 620px;
	margin: 0 auto;
	padding: 16px;
	border: 1px solid lightgray;
	border-radius: 16px;
}

.card p:last-child {
	margin-bottom: 0;
}