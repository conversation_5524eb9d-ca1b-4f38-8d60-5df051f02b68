<!DOCTYPE html>
<html lang="pt">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sistema de Registos Hospitalares</title>
    <!-- Styles -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="stylesheet" href="css/styles.css" />
  </head>
  <body class="bg-gray-100 font-sans">
    <div class="flex h-screen overflow-hidden">
      <!-- Sidebar -->
      <div class="sidebar bg-blue-800 text-white w-64 flex flex-col">
        <div
          class="p-4 flex items-center justify-between border-b border-blue-700"
        >
          <div class="flex items-center">
            <i class="fas fa-hospital text-2xl mr-2"></i>
            <span class="logo-text text-xl font-bold">HospitalSys</span>
          </div>
          <button id="toggle-sidebar" class="text-white focus:outline-none">
            <i class="fas fa-bars"></i>
          </button>
        </div>
        <div class="flex-1 overflow-y-auto">
          <nav class="p-4">
            <div class="mb-6">
              <p
                class="text-blue-300 uppercase text-xs font-semibold sidebar-text"
              >
                Menu Principal
              </p>
              <ul class="mt-2">
                <li class="mb-1">
                  <button
                    data-section="dashboard-content"
                    id="dashboard-link"
                    class="flex items-center p-2 text-white rounded hover:bg-blue-700 transition w-full text-left"
                  >
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    <span class="sidebar-text">Dashboard</span>
                  </button>
                </li>
                <li class="mb-1">
                  <button
                    data-section="exams-content"
                    id="exams-link"
                    class="flex items-center p-2 text-white rounded hover:bg-blue-700 transition w-full text-left"
                  >
                    <i class="fas fa-flask mr-3"></i>
                    <span class="sidebar-text">Registo de Exames</span>
                  </button>
                </li>
                <li class="mb-1">
                  <button
                    data-section="mail-content"
                    id="mail-link"
                    class="flex items-center p-2 text-white rounded hover:bg-blue-700 transition w-full text-left"
                  >
                    <i class="fas fa-envelope mr-3"></i>
                    <span class="sidebar-text">Correio Interno</span>
                  </button>
                </li>
                <li class="mb-1">
                  <button
                    data-section="users-content"
                    id="users-link"
                    class="flex items-center p-2 text-white rounded hover:bg-blue-700 transition w-full text-left"
                  >
                    <i class="fas fa-users mr-3"></i>
                    <span class="sidebar-text">Gestão de Utilizadores</span>
                  </button>
                </li>
                <li class="mb-1">
                  <button
                    data-section="reports-content"
                    id="reports-link"
                    class="flex items-center p-2 text-white rounded hover:bg-blue-700 transition w-full text-left"
                  >
                    <i class="fas fa-file-excel mr-3"></i>
                    <span class="sidebar-text">Relatórios</span>
                  </button>
                </li>
                <li class="mb-1">
                  <button
                    data-section="scanner-content"
                    id="scanner-link"
                    class="flex items-center p-2 text-white rounded hover:bg-blue-700 transition w-full text-left"
                  >
                    <i class="fas fa-barcode mr-3"></i>
                    <span class="sidebar-text">Leitor de Códigos</span>
                  </button>
                </li>
              </ul>
            </div>
          </nav>
        </div>
        <div class="p-4 border-t border-blue-700">
          <div class="flex items-center">
            <img
              src="https://ui-avatars.com/api/?name=Admin"
              alt="User"
              class="w-8 h-8 rounded-full"
            />
            <div class="ml-3 sidebar-text">
              <p class="text-sm font-medium" id="current-user">Admin</p>
              <p class="text-xs text-blue-300">Administrador</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="main-content flex-1 overflow-y-auto">
        <!-- Top Navigation -->
        <header class="bg-white shadow-sm">
          <div class="flex items-center justify-between p-4">
            <h1 class="text-2xl font-semibold text-gray-800" id="page-title">
              Dashboard
            </h1>
            <div class="flex items-center space-x-4">
              <button
                class="p-2 text-gray-600 hover:text-blue-600 focus:outline-none"
              >
                <i class="fas fa-bell"></i>
              </button>
            </div>
          </div>
        </header>

        <!-- Page Content -->
        <main class="p-6">
          <div id="dashboard-content" class="content-section">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-gray-500">Exames Registados</p>
                    <h3 class="text-2xl font-bold" id="total-exams">0</h3>
                  </div>
                  <div class="bg-blue-100 p-3 rounded-full">
                    <i class="fas fa-flask text-blue-600 text-xl"></i>
                  </div>
                </div>
              </div>
              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-gray-500">Correspondência</p>
                    <h3 class="text-2xl font-bold" id="total-mail">0</h3>
                  </div>
                  <div class="bg-green-100 p-3 rounded-full">
                    <i class="fas fa-envelope text-green-600 text-xl"></i>
                  </div>
                </div>
              </div>
              <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-gray-500">Utilizadores</p>
                    <h3 class="text-2xl font-bold" id="total-users">1</h3>
                  </div>
                  <div class="bg-purple-100 p-3 rounded-full">
                    <i class="fas fa-users text-purple-600 text-xl"></i>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow mb-6">
              <h2 class="text-xl font-semibold mb-4">Atividade Recente</h2>
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data/Hora</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utilizador</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ação</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Detalhes</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200" id="recent-activity"></tbody>
                </table>
              </div>
            </div>
          </div>

          <div id="exams-content" class="content-section hidden">
            <div class="bg-white p-6 rounded-lg shadow mb-6">
              <h2 class="text-xl font-semibold mb-4">Registo de Exames</h2>
              <form id="exam-form" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label for="patient-id" class="block text-sm font-medium text-gray-700">Nº Utente</label>
                    <div class="mt-1 flex rounded-md shadow-sm">
                      <input type="text" id="patient-id" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                      <button type="button" id="scan-patient" class="ml-2 inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-barcode mr-1"></i> Ler
                      </button>
                    </div>
                  </div>
                  <div>
                    <label for="patient-name" class="block text-sm font-medium text-gray-700">Nome do Utente</label>
                    <input type="text" id="patient-name" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                  </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label for="exam-type" class="block text-sm font-medium text-gray-700">Tipo de Exame</label>
                    <select id="exam-type" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                      <option value="">Selecione...</option>
                      <option value="Hemograma">Hemograma</option>
                      <option value="Bioquímica">Bioquímica</option>
                      <option value="Urina">Urina</option>
                      <option value="Raio-X">Raio-X</option>
                      <option value="Ressonância">Ressonância</option>
                      <option value="Ecografia">Ecografia</option>
                    </select>
                  </div>
                  <div>
                    <label for="exam-date" class="block text-sm font-medium text-gray-700">Data do Exame</label>
                    <input type="date" id="exam-date" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                  </div>
                  <div>
                    <label for="exam-status" class="block text-sm font-medium text-gray-700">Estado</label>
                    <select id="exam-status" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                      <option value="Agendado">Agendado</option>
                      <option value="Realizado">Realizado</option>
                      <option value="Cancelado">Cancelado</option>
                    </select>
                  </div>
                </div>
                <div>
                  <label for="exam-notes" class="block text-sm font-medium text-gray-700">Observações</label>
                  <textarea id="exam-notes" rows="3" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                  <button type="reset" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">Limpar</button>
                  <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"><i class="fas fa-save mr-2"></i> Guardar</button>
                </div>
              </form>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
              <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold">Exames Registados</h2>
                <div class="flex items-center space-x-2">
                  <input type="text" id="search-exams" placeholder="Pesquisar..." class="px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <button id="delete-selected-exams" class="hidden inline-flex items-center px-3 py-1 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"><i class="fas fa-trash-alt mr-1"></i> Apagar</button>
                  <button id="export-exams" class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"><i class="fas fa-file-excel mr-1"></i> Exportar</button>
                  <button id="print-exams" class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"><i class="fas fa-print mr-1"></i> Imprimir</button>
                </div>
              </div>
              <div class="overflow-x-auto">
                <table id="exams-table" class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50 rounded-tl-lg rounded-tr-lg">
                    <tr>
                      <th class="px-6 py-3 text-left"><input type="checkbox" id="select-all-exams" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"></th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nº Utente</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nome</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo de Exame</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estado</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registado por</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200" id="exams-table-body"></tbody>
                </table>
              </div>
            </div>
          </div>

          <div id="mail-content" class="content-section hidden">
            <div class="bg-white p-6 rounded-lg shadow mb-6">
              <h2 class="text-xl font-semibold mb-4">Registo de Correio Interno</h2>
              <form id="mail-form" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label for="mail-code" class="block text-sm font-medium text-gray-700">Código</label>
                    <div class="mt-1 flex rounded-md shadow-sm">
                      <input type="text" id="mail-code" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                      <button type="button" id="scan-mail" class="ml-2 inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"><i class="fas fa-barcode mr-1"></i> Ler</button>
                    </div>
                  </div>
                  <div id="barcode-preview-container" class="hidden">
                    <label class="block text-sm font-medium text-gray-700">Pré-visualização</label>
                    <div class="barcode-preview flex justify-center items-center bg-white p-2 rounded border"><svg id="barcode-preview-svg"></svg></div>
                  </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label for="mail-sender" class="block text-sm font-medium text-gray-700">Remetente</label>
                    <input type="text" id="mail-sender" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                  </div>
                  <div>
                    <label for="mail-recipient" class="block text-sm font-medium text-gray-700">Destinatário</label>
                    <input type="text" id="mail-recipient" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                  </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label for="mail-type" class="block text-sm font-medium text-gray-700">Tipo</label>
                    <select id="mail-type" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                      <option value="">Selecione...</option>
                      <option value="Documento">Documento</option>
                      <option value="Amostra">Amostra</option>
                      <option value="Equipamento">Equipamento</option>
                      <option value="Outro">Outro</option>
                    </select>
                  </div>
                  <div>
                    <label for="mail-date" class="block text-sm font-medium text-gray-700">Data</label>
                    <input type="date" id="mail-date" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                  </div>
                  <div>
                    <label for="mail-status" class="block text-sm font-medium text-gray-700">Estado</label>
                    <select id="mail-status" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                      <option value="Registado">Registado</option>
                      <option value="Entregue">Entregue</option>
                      <option value="Pendente">Pendente</option>
                    </select>
                  </div>
                </div>
                <div>
                  <label for="mail-description" class="block text-sm font-medium text-gray-700">Descrição</label>
                  <textarea id="mail-description" rows="3" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                  <button type="reset" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">Limpar</button>
                  <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"><i class="fas fa-save mr-2"></i> Guardar</button>
                </div>
              </form>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
              <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold">Correio Registado</h2>
                <div class="flex items-center space-x-2">
                  <input type="text" id="search-mail" placeholder="Pesquisar..." class="px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <button id="delete-selected-mail" class="hidden inline-flex items-center px-3 py-1 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"><i class="fas fa-trash-alt mr-1"></i> Apagar</button>
                  <button id="export-mail" class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"><i class="fas fa-file-excel mr-1"></i> Exportar</button>
                  <button id="print-mail" class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"><i class="fas fa-print mr-1"></i> Imprimir</button>
                </div>
              </div>
              <div class="overflow-x-auto">
                <table id="mail-table" class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50 rounded-tl-lg rounded-tr-lg">
                    <tr>
                      <th class="px-6 py-3 text-left"><input type="checkbox" id="select-all-mail" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"></th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Código</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remetente</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Destinatário</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estado</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registado por</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200" id="mail-table-body"></tbody>
                </table>
              </div>
            </div>
          </div>

          <div id="users-content" class="content-section hidden">
            <div class="bg-white p-6 rounded-lg shadow mb-6">
              <h2 class="text-xl font-semibold mb-4">Adicionar/Editar Utilizador</h2>
              <form id="user-form" class="space-y-4">
                <input type="hidden" id="user-id-editing">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label for="user-name" class="block text-sm font-medium text-gray-700">Nome Completo</label>
                    <input type="text" id="user-name" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                  </div>
                  <div>
                    <label for="user-username" class="block text-sm font-medium text-gray-700">Username</label>
                    <input type="text" id="user-username" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                  </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label for="user-email" class="block text-sm font-medium text-gray-700">Email</label>
                    <input type="email" id="user-email" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                  </div>
                  <div>
                    <label for="user-role" class="block text-sm font-medium text-gray-700">Função</label>
                    <select id="user-role" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                      <option>Administrador</option>
                      <option>Técnico</option>
                      <option>Rececionista</option>
                    </select>
                  </div>
                </div>
                <div class="flex justify-end space-x-3">
                  <button type="reset" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">Limpar</button>
                  <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">Guardar Utilizador</button>
                </div>
              </form>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
              <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold">Utilizadores Registados</h2>
                <div class="flex items-center space-x-2">
                  <input type="text" id="search-users" placeholder="Pesquisar..." class="px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <button id="delete-selected-users" class="hidden inline-flex items-center px-3 py-1 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"><i class="fas fa-trash-alt mr-1"></i> Apagar</button>
                </div>
              </div>
              <div class="overflow-x-auto">
                <table id="users-table" class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left"><input type="checkbox" id="select-all-users" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"></th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nome</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Função</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                    </tr>
                  </thead>
                  <tbody id="users-table-body" class="bg-white divide-y divide-gray-200"></tbody>
                </table>
              </div>
            </div>
          </div>

          <div id="reports-content" class="content-section hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Exames por Tipo</h2>
                <canvas id="exams-by-type-chart"></canvas>
              </div>
              <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Atividade nos Últimos 7 Dias</h2>
                <canvas id="activity-by-day-chart"></canvas>
              </div>
            </div>
          </div>

          <div id="scanner-content" class="content-section hidden">
            <div class="bg-white p-6 rounded-lg shadow mb-6">
              <h2 class="text-xl font-semibold mb-4">Leitor de Códigos de Barras</h2>
              <div class="space-y-4">
                <div class="mb-4">
                  <p class="text-gray-600 mb-2">Utilize o leitor de códigos de barras para escanear códigos e QR codes.</p>
                  <div class="flex flex-col space-y-4">
                    <button id="start-scanner" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"><i class="fas fa-camera mr-2"></i> Iniciar Câmera</button>
                    <button id="stop-scanner" class="hidden mt-4 inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"><i class="fas fa-times mr-2"></i> Parar Scanner</button>
                    <div id="scanner-result" class="mt-4 p-4 border rounded-md bg-gray-50 hidden">
                      <div id="scan-result-text" class="text-gray-900 font-mono break-all"></div>
                    </div>
                  </div>
                </div>
                <div id="qr-reader-container" class="hidden">
                  <div id="qr-reader" class="w-full max-w-md mx-auto border rounded-lg overflow-hidden"></div>
                </div>
                <div class="mt-8">
                  <div class="flex justify-between items-center mb-3">
                    <h3 class="text-lg font-medium text-gray-900">Histórico de Leituras</h3>
                    <button id="clear-history" class="text-sm text-red-600 hover:text-red-800"><i class="fas fa-trash-alt mr-1"></i> Limpar Histórico</button>
                  </div>
                  <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-50">
                        <tr>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data/Hora</th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Código</th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                        </tr>
                      </thead>
                      <tbody id="scan-history" class="bg-white divide-y divide-gray-200"></tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <!-- QR Code Modal -->
    <div id="qr-code-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Registo Guardado com Sucesso!</h3>
          <div class="mt-2 px-7 py-3">
            <p class="text-sm text-gray-500">Aponte a câmara para o QR Code para ler os dados noutro dispositivo.</p>
            <div id="qr-code-container" class="mt-4 flex justify-center"></div>
            <p id="qr-code-text" class="text-xs text-gray-400 mt-2"></p>
          </div>
          <div class="items-center px-4 py-3">
            <button id="close-qr-modal" class="px-4 py-2 bg-blue-600 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-300">Fechar</button>
          </div>
        </div>
      </div>
    </div>

    <!-- External Libraries -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.sheetjs.com/xlsx-0.19.3/package/dist/xlsx.full.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="https://unpkg.com/html5-qrcode@2.3.8/dist/html5-qrcode.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.2/dist/chart.umd.min.js"></script>

    <!-- Local Scripts -->
    <script src="js/main.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/exams.js"></script>
    <script src="js/mail.js"></script>
    <script src="js/scanner.js"></script>
    <script src="js/users.js"></script>
    <script src="js/reports.js"></script>
  </body>
</html>